package nats_service

import (
	"liteframe/internal/common/natsrpc"
	"liteframe/internal/common/protos/public"
)

const (
	channelSize     = 1024
	maxMatchPerTick = 500
	maxMatchTimeout = 60
)

type PlayerInfo struct {
	battleInfo *public.PBBattlePlayerInfo
	teamInfo   *public.PBBattleTeamInfo
	beginTime  int64
}

/*
singleMatch 管理玩家匹配, 匹配成功后请求room server创建房间, 通知game server匹配结果
*/

type IMatch interface {
	Match(req *natsrpc.MatchRequest)
	CancelMatch(req *natsrpc.CancelMatchRequest)
	Start()
	Stop()
}

type matchManager struct {
	//pveDouTeamMatch IMatch
	//arenaMatch      IMatch
	//peakArenaMatch  IMatch
	normalMatch     IMatch
	autoChessMatch  *AutoChessMatch
}

func newMatchManager() *matchManager {
	ret := &matchManager{}
	ret.init()
	return ret
}

func (m *matchManager) init() {
	//m.pveDouTeamMatch = newMatchDouPveService()
	//m.arenaMatch = newArenaMatch()
	//m.peakArenaMatch = newPeakArenaMatch()
	m.normalMatch = newNormalMatch()
	m.autoChessMatch = newAutoChessMatch()
}

func (m *matchManager) CancelMatch(req *natsrpc.CancelMatchRequest) {
	//imp := m.GetMatchImpl(1)
	//if imp == nil {
	//	return
	//}
	//imp.CancelMatch(req)

	// 同时取消普通匹配和自动象棋匹配
	m.normalMatch.CancelMatch(req)
	m.autoChessMatch.CancelMatch(req)
}

func (m *matchManager) Run() {
	//m.pveDouTeamMatch.Start()
	//m.peakArenaMatch.Start()
	//m.arenaMatch.Start()
	m.normalMatch.Start()
	m.autoChessMatch.Start()
}

func (m *matchManager) Stop() {
	//m.pveDouTeamMatch.Stop()
	//m.arenaMatch.Stop()
	//m.peakArenaMatch.Stop()
	m.normalMatch.Stop()
	m.autoChessMatch.Stop()
}
func (m *matchManager) RequestMatch(req *natsrpc.MatchRequest) {
	//imp := m.GetMatchImpl(1)
	//if imp == nil {
	//	return
	//}
	//imp.Match(req)

	// 根据请求类型路由到不同的匹配器
	// TODO: 添加请求类型字段来区分普通匹配和自动象棋匹配
	// 暂时根据玩家等级或其他字段判断
	if m.isAutoChessMatch(req) {
		m.autoChessMatch.Match(req)
	} else {
		m.normalMatch.Match(req)
	}
}

// 判断是否为自动象棋匹配
func (m *matchManager) isAutoChessMatch(req *natsrpc.MatchRequest) bool {
	// TODO: 根据实际业务逻辑判断
	// 可以通过玩家等级、特殊标识、或新增的匹配类型字段判断
	// 暂时使用简单逻辑：奖杯数大于100的进入自动象棋匹配
	return req.Player.Throphy > 100
}

//func (m *matchManager) GetMatchImpl(typ comm.BattleType) IMatch {
//	switch typ {
//	case comm.BattleType_BT_Dou_PVE:
//		return m.pveDouTeamMatch
//	case comm.BattleType_BT_Arena:
//		return m.arenaMatch
//	case comm.BattleType_BT_PeakArena:
//		return m.peakArenaMatch
//	}
//	return nil
//}
