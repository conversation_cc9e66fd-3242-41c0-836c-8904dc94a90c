"G:\JetBrains\JetBrains Rider 2024.3.5\plugins\dpa\DotFiles\JetBrains.DPA.Runner.exe" --handle=15580 --backend-pid=21088 --etw-collect-flags=67108622 --detach-event-name=dpa.detach.21088.37 --refresh-interval=1 -- G:/ZZZ/src/Main/Server/liteframe/bin/BattleServer.exe
MainThread Start.
MainThread LoadAndInitAppAssembly Start...
MainThread LoadAndInitAppAssembly End...
ThreadManager init begin.
ThreadManager init end.
[WorkUnit] WorkUnit 1 Start With Owner GlobalManager_ThreadManager.
=== 环境变量 ===
DOTNET_gcServer=
DOTNET_GCHeapCount: 
DOTNET_gcConcurrent: 
DOTNET_GCDynamicAdaptationMode: 
===================
=== .NET GC 配置 ===
GC 模式: Workstation GC
GC的LatencyMode模式: Interactive
LOH 压缩模式: Default
===================
BattleConfig initialized with PlayMode ID: 1, Type: 1, Scene: 1001
BattleConfig initialized successfully
NATS client configuration not found, using server connection for client
Status check timer started (10 second interval)
-------------【服务器程序加载完毕】-----------------------
------------------------------------------------------
[WorkUnit] WorkUnit 1 Enter Thread 0.
SceneManager Start
[WorkUnit] WorkUnit 2 Start With Owner GlobalManager_SceneManager.
[WorkUnit] WorkUnit 2 Enter Thread 31.
[BattleService] Player 10102021301 (奢侈的.战士) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000095932 (AI_奢侈的.战士) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000011436 (AI_奢侈的.战士) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000022651 (AI_奢侈的.战士) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleStateManager_430582329311233] Initialized
[PlayerManager_430582329311233] Player 10102021301 (奢侈的.战士) Trophy 1 Health 3
[PlayerManager_430582329311233] Player 90000095932 (AI_奢侈的.战士) Trophy 1 Health 3
[PlayerManager_430582329311233] Player 90000011436 (AI_奢侈的.战士) Trophy 1 Health 3
[PlayerManager_430582329311233] Player 90000022651 (AI_奢侈的.战士) Trophy 1 Health 3
[PlayerManager_430582329311233] Initialized with 4 players
[BattleInstanceManager] Initialized for battle 430582329311233
[OpponentPairManager] Initialized for battle 430582329311233
[BuffManager_430582329311233] Initialized
[CheckerBoard_430582329311233] Cleared checkerboard
[CheckerBoard_430582329311233] Initialized
[AutoChessScene_430582329311233] Event handlers registered
[AutoChessScene_430582329311233] Battle 430582329311233 initialized with 4 players, waiting for all players to enter
[SceneManager] Added AutoChessScene 430582329311233 to thread management
[BattleService] Battle 430582329311233 created successfully with 4 players
[BattleService] Battle 430582329311233 is now waiting for all players to call EnterBattle RPC
[BattleService] Current battle state: StateNone (waiting for player entry)
[BattleService] Players expected to enter: [10102021301, 90000095932, 90000011436, 90000022651]
[BattleService] ===== IMPORTANT: Battle will NOT start until ALL players call EnterBattle =====
[BattleService] ===== GameServer should call EnterBattle for each player after scene loading =====
[BattleService] ===== BOT STRATEGY: Player 10102021301 is real player, others are bots =====
[BattleService] Auto-entered bot player 90000095932 immediately
[BattleService] Auto-entered bot player 90000011436 immediately
[BattleService] Auto-entered bot player 90000022651 immediately
[BattleService] Battle 430582329311233 bots auto-entered: 3/4 players ready
[BattleService] Status: 1 waiting (3/4 entered), 0 active
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10102021301 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10102021301 entered battle 430582329311233 (initial), current count: 4
[BattleService] All 4 players entered battle 430582329311233, starting battle state machine
[BattleService] Entered players: [90000095932, 90000011436, 90000022651, 10102021301]
[BattleService] Triggering AutoChessScene.StartBattleStateMachine() for battle 430582329311233
[AutoChessScene_430582329311233] StartBattleStateMachine() called for battle 430582329311233
[AutoChessScene_430582329311233] BattleStateManager is ready, starting first round...
[AutoChessScene_430582329311233] Current state before starting: StateNone
[BattleStateManager_430582329311233] ===== STARTING NEW ROUND 1 =====
[BattleStateManager_430582329311233] Round 1 has buff selection: False
[BattleStateManager_430582329311233] Publishing RoundStartedEvent for round 1
[AutoChessScene_430582329311233] Round 1 started
[BattleStateManager_430582329311233] Setting state to StateRoundStart for round 1
[BattleStateManager_430582329311233] State: StateNone -> StateRoundStart (R1, 1000ms)
[AutoChessScene_430582329311233] HandleRoundStart: 4 active players
[AutoChessScene_430582329311233] Valid player: 10102021301, Health: 3
[AutoChessScene_430582329311233] Valid player: 90000095932, Health: 3
[AutoChessScene_430582329311233] Valid player: 90000011436, Health: 3
[AutoChessScene_430582329311233] Valid player: 90000022651, Health: 3
[AutoChessScene_430582329311233] No instance found for player 10102021301 when saving board data
[AutoChessScene_430582329311233] No instance found for player 90000095932 when saving board data
[AutoChessScene_430582329311233] No instance found for player 90000011436 when saving board data
[AutoChessScene_430582329311233] No instance found for player 90000022651 when saving board data
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000022651 vs Player 10102021301
[OpponentPairManager] Random pair: Player 90000095932 vs Player 90000011436
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_430582329311233] Created 4 opponent pairs
[PlayerManager_430582329311233] Set player opponents, count: 4
[BattleInstanceManager] Created instance 430582329311233_1 for active players 90000022651 vs 10102021301
[CheckerBoard_430582329311233] Cleared checkerboard
[CheckerBoard_430582329311233] Initialized
[BattleInstance] 430582329311233_1 created with players: 90000022651, 10102021301
[BattleInstanceManager] Created instance 430582329311233_2 for active players 90000095932 vs 90000011436
[CheckerBoard_430582329311233] Cleared checkerboard
[CheckerBoard_430582329311233] Initialized
[BattleInstance] 430582329311233_2 created with players: 90000095932, 90000011436
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 10102021301
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 90000095932
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 90000011436
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 90000022651
[PlayerManager_430582329311233] Reset all players ready status
[AutoChessScene_430582329311233] Round started with 2 battle instances
[AutoChessScene_430582329311233] Generating 5 heroes for all players in round 1
[CheckerBoard_430582329311233] Placed entity 1 at position (6, 6)
[CheckerBoard_430582329311233] Created entity ID:1 ConfigID:101 at (6, 6) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 2 at position (8, 4)
[CheckerBoard_430582329311233] Created entity ID:2 ConfigID:101 at (8, 4) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 3 at position (9, 1)
[CheckerBoard_430582329311233] Created entity ID:3 ConfigID:103 at (9, 1) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 4 at position (8, 3)
[CheckerBoard_430582329311233] Created entity ID:4 ConfigID:102 at (8, 3) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 5 at position (9, 6)
[CheckerBoard_430582329311233] Created entity ID:5 ConfigID:102 at (9, 6) for player 10102021301
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 10102021301: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 10102021301 in Enemy area
[AutoChessScene_430582329311233] Generated 5 heroes for player 10102021301: 5 placed on board, 0 in temporary slots
[CheckerBoard_430582329311233] Placed entity 1 at position (4, 5)
[CheckerBoard_430582329311233] Created entity ID:1 ConfigID:101 at (4, 5) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 2 at position (4, 3)
[CheckerBoard_430582329311233] Created entity ID:2 ConfigID:103 at (4, 3) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 3 at position (4, 6)
[CheckerBoard_430582329311233] Created entity ID:3 ConfigID:101 at (4, 6) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 4 at position (1, 2)
[CheckerBoard_430582329311233] Created entity ID:4 ConfigID:101 at (1, 2) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 5 at position (2, 4)
[CheckerBoard_430582329311233] Created entity ID:5 ConfigID:101 at (2, 4) for player 90000095932
[CheckerBoard_430582329311233] CheckTimes limit (4) reached for 1 hero types for player 90000095932
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 90000095932: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 90000095932 in My area
[AutoChessScene_430582329311233] Generated 5 heroes for player 90000095932: 5 placed on board, 0 in temporary slots
[CheckerBoard_430582329311233] Placed entity 6 at position (7, 5)
[CheckerBoard_430582329311233] Created entity ID:6 ConfigID:102 at (7, 5) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 7 at position (10, 4)
[CheckerBoard_430582329311233] Created entity ID:7 ConfigID:103 at (10, 4) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 8 at position (9, 3)
[CheckerBoard_430582329311233] Created entity ID:8 ConfigID:103 at (9, 3) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 9 at position (7, 4)
[CheckerBoard_430582329311233] Created entity ID:9 ConfigID:102 at (7, 4) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 10 at position (10, 6)
[CheckerBoard_430582329311233] Created entity ID:10 ConfigID:103 at (10, 6) for player 90000011436
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 90000011436: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 90000011436 in Enemy area
[AutoChessScene_430582329311233] Generated 5 heroes for player 90000011436: 5 placed on board, 0 in temporary slots
[CheckerBoard_430582329311233] Placed entity 6 at position (4, 4)
[CheckerBoard_430582329311233] Created entity ID:6 ConfigID:102 at (4, 4) for player 90000022651
[CheckerBoard_430582329311233] Placed entity 7 at position (5, 2)
[CheckerBoard_430582329311233] Created entity ID:7 ConfigID:103 at (5, 2) for player 90000022651
[CheckerBoard_430582329311233] Placed entity 8 at position (5, 4)
[CheckerBoard_430582329311233] Created entity ID:8 ConfigID:102 at (5, 4) for player 90000022651
[CheckerBoard_430582329311233] Placed entity 9 at position (5, 3)
[CheckerBoard_430582329311233] Created entity ID:9 ConfigID:103 at (5, 3) for player 90000022651
[CheckerBoard_430582329311233] Placed entity 10 at position (2, 3)
[CheckerBoard_430582329311233] Created entity ID:10 ConfigID:101 at (2, 3) for player 90000022651
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 90000022651: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 90000022651 in My area
[AutoChessScene_430582329311233] Generated 5 heroes for player 90000022651: 5 placed on board, 0 in temporary slots
[AutoChessScene_430582329311233] Player status: Total=4, Active=4
[AutoChessScene_430582329311233] Player 10102021301: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430582329311233] Player 90000095932: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430582329311233] Player 90000011436: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430582329311233] Player 90000022651: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430582329311233] Sending RoundStart notifications to 4 active players...
[AutoChessScene_430582329311233] RoundStart board data: player:90000022651 heroes:5
[AutoChessScene_430582329311233] RoundStart board data: player:10102021301 heroes:5
[AutoChessScene_430582329311233] Sending RoundStart to Player 10102021301 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430582329311233] RoundStart board data: player:90000095932 heroes:5
[AutoChessScene_430582329311233] RoundStart board data: player:90000011436 heroes:5
[AutoChessScene_430582329311233] Sending RoundStart to Player 90000095932 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430582329311233] RoundStart board data: player:90000095932 heroes:5
[AutoChessScene_430582329311233] RoundStart board data: player:90000011436 heroes:5
[AutoChessScene_430582329311233] Sending RoundStart to Player 90000011436 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430582329311233] RoundStart board data: player:90000022651 heroes:5
[AutoChessScene_430582329311233] RoundStart board data: player:10102021301 heroes:5
[AutoChessScene_430582329311233] Sending RoundStart to Player 90000022651 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430582329311233] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 430582329311233 state to StateRoundStart
[AutoChessScene_430582329311233] State change sent to GameServer: StateNone -> StateRoundStart (R1)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleStateManager_430582329311233] ===== ROUND 1 INITIALIZATION COMPLETE =====
[AutoChessScene_430582329311233] Battle state machine started successfully for battle 430582329311233
[AutoChessScene_430582329311233] Current state after starting: StateRoundStart
[BattleService] Battle state update result: False, new state: StateRoundStart
[BattleStateManager_430582329311233] State: StateRoundStart -> StatePreparation (R1, 65000ms)
[AutoChessScene_430582329311233] Preparation phase started
[PlayerManager_430582329311233] Player 90000095932 ready status set to True
[PlayerManager_430582329311233] Player 90000011436 ready status set to True
[PlayerManager_430582329311233] Player 90000022651 ready status set to True
[AutoChessScene_430582329311233] Auto-ready 3 additional bots
[AutoChessScene_430582329311233] Free operation phase started
[BattleService] Updated battle 430582329311233 state to StatePreparation
[AutoChessScene_430582329311233] State change sent to GameServer: StateRoundStart -> StatePreparation (R1)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[PlayerManager_430582329311233] Player 10102021301 ready status set to True
[PlayerManager_430582329311233] All players are ready!
[AutoChessScene_430582329311233] All players are ready, transitioning to next state
[BattleStateManager_430582329311233] State: StatePreparation -> StateBattleStarting (R1, 1000ms)
[AutoChessScene_430582329311233] Applying battle start buffs for all players
[AutoChessScene_430582329311233] Camp info for player 90000022651: 5 heroes added
[AutoChessScene_430582329311233] Camp info for player 10102021301: 5 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 10102021301, Team order: [90000022651, 10102021301], total GridIDs used: 10
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 10102021301 vs Opponent 90000022651 with 2 teams
[AutoChessScene_430582329311233] Camp info for player 90000095932: 5 heroes added
[AutoChessScene_430582329311233] Camp info for player 90000011436: 5 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 90000095932, Team order: [90000095932, 90000011436], total GridIDs used: 10
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 90000095932 vs Opponent 90000011436 with 2 teams
[AutoChessScene_430582329311233] Camp info for player 90000095932: 5 heroes added
[AutoChessScene_430582329311233] Camp info for player 90000011436: 5 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 90000011436, Team order: [90000095932, 90000011436], total GridIDs used: 10
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 90000011436 vs Opponent 90000095932 with 2 teams
[AutoChessScene_430582329311233] Camp info for player 90000022651: 5 heroes added
[AutoChessScene_430582329311233] Camp info for player 10102021301: 5 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 90000022651, Team order: [90000022651, 10102021301], total GridIDs used: 10
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 90000022651 vs Opponent 10102021301 with 2 teams
[AutoChessScene_430582329311233] Sent RoundBattleStart notifications with seed: 23777461
[BattleService] Updated battle 430582329311233 state to StateBattleStarting
[AutoChessScene_430582329311233] State change sent to GameServer: StatePreparation -> StateBattleStarting (R1)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[AutoChessScene_430582329311233] Player 10102021301 set ready status to True
[BattleStateManager_430582329311233] State: StateBattleStarting -> StateBattleInProgress (R1, 65000ms)
[AutoChessScene_430582329311233] Starting all battle instances
[BattleInstance] 430582329311233_1 battle started
[BattleInstance] 430582329311233_2 battle started
[BattleInstanceManager] Started all 2 battle instances
[AutoChessScene_430582329311233] Auto EndBattle for bot 90000095932 vs bot 90000011436, random result: bot 90000095932 wins = True
[AutoChessScene_430582329311233] Player 90000095932 sent EndBattleReq (win: True), instance: 430582329311233_2
[AutoChessScene_430582329311233] Waiting for opponent 90000011436 to send EndBattleReq for instance 430582329311233_2
[AutoChessScene_430582329311233] Player 90000011436 sent EndBattleReq (win: False), instance: 430582329311233_2
[BattleInstance] 430582329311233_2 battle finished, winner: 90000095932, loser: 90000011436
[AutoChessScene_430582329311233] Battle instance 430582329311233_2 completed: Winner 90000095932, Loser 90000011436
[AutoChessScene_430582329311233] Bot 90000022651 vs real player 10102021301, waiting for real player result
[AutoChessScene_430582329311233] Bot vs real player battles will be handled by system timeout (62s)
[BattleService] Updated battle 430582329311233 state to StateBattleInProgress
[AutoChessScene_430582329311233] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R1)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] EndBattle uid: 10102021301, win: True
[AutoChessScene_430582329311233] Player 10102021301 sent EndBattleReq (win: True), instance: 430582329311233_1
[AutoChessScene_430582329311233] Auto EndBattle for bot 90000022651 vs real player 10102021301, bot result: False
[BattleInstance] 430582329311233_1 battle finished, winner: 10102021301, loser: 90000022651
[AutoChessScene_430582329311233] Battle instance 430582329311233_1 completed: Winner 10102021301, Loser 90000022651
[AutoChessScene_430582329311233] All battle instances finished, proceeding to settlement
[BattleStateManager_430582329311233] State: StateBattleInProgress -> StateRoundSettlement (R1, 5000ms)
[AutoChessScene_430582329311233] Processing battle results
[PlayerManager_430582329311233] Player 90000022651 health reduced by 1, current health: 2
[AutoChessScene_430582329311233] Player 90000022651 lost 1 health, winner: 10102021301
[AutoChessScene_430582329311233] Sent RoundBattleEnd notification (isEnd=false) - Winner: 10102021301, Loser: 90000022651
[PlayerManager_430582329311233] Player 90000011436 health reduced by 1, current health: 2
[AutoChessScene_430582329311233] Player 90000011436 lost 1 health, winner: 90000095932
[AutoChessScene_430582329311233] Sent RoundBattleEnd notification (isEnd=false) - Winner: 90000095932, Loser: 90000011436
[BattleStateManager_430582329311233] Invalid state transition from StateRoundSettlement to StateRoundSettlement
[AutoChessScene_430582329311233] Checking players elimination
[AutoChessScene_430582329311233] Active players remaining: 4
[AutoChessScene_430582329311233] Waiting for all players to confirm round settlement before starting new round
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000095932
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000011436
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000022651
[BattleService] Updated battle 430582329311233 state to StateRoundSettlement
[AutoChessScene_430582329311233] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R1)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10102021301 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10102021301 confirmed round settlement, current state: StateRoundSettlement
[AutoChessScene_430582329311233] Player 10102021301 confirmed round settlement, count: 4
[AutoChessScene_430582329311233] Real player 10102021301 confirmed, auto-confirming all bots
[AutoChessScene_430582329311233] All players confirmed round settlement, starting new round
[PlayerManager_430582329311233] Player 10102021301 ready status set to False
[PlayerManager_430582329311233] Player 90000095932 ready status set to False
[PlayerManager_430582329311233] Player 90000011436 ready status set to False
[PlayerManager_430582329311233] Player 90000022651 ready status set to False
[BattleStateManager_430582329311233] ===== STARTING NEW ROUND 2 =====
[BattleStateManager_430582329311233] Round 2 has buff selection: True
[BattleStateManager_430582329311233] Publishing RoundStartedEvent for round 2
[AutoChessScene_430582329311233] Round 2 started
[BattleStateManager_430582329311233] Setting state to StateRoundStart for round 2
[BattleStateManager_430582329311233] State: StateRoundSettlement -> StateRoundStart (R2, 1000ms)
[AutoChessScene_430582329311233] HandleRoundStart: 4 active players
[AutoChessScene_430582329311233] Valid player: 10102021301, Health: 3
[AutoChessScene_430582329311233] Valid player: 90000095932, Health: 3
[AutoChessScene_430582329311233] Valid player: 90000011436, Health: 2
[AutoChessScene_430582329311233] Valid player: 90000022651, Health: 2
[AutoChessScene_430582329311233] Player 10102021301 has 5 entities to save
[PlayerManager_430582329311233] Saved board data: player:10102021301 entities:5
[PlayerManager_430582329311233] Saved prev round data: player:10102021301 entities:5
[AutoChessScene_430582329311233] Player 90000095932 has 5 entities to save
[PlayerManager_430582329311233] Saved board data: player:90000095932 entities:5
[PlayerManager_430582329311233] Saved prev round data: player:90000095932 entities:5
[AutoChessScene_430582329311233] Player 90000011436 has 5 entities to save
[PlayerManager_430582329311233] Saved board data: player:90000011436 entities:5
[PlayerManager_430582329311233] Saved prev round data: player:90000011436 entities:5
[AutoChessScene_430582329311233] Player 90000022651 has 5 entities to save
[PlayerManager_430582329311233] Saved board data: player:90000022651 entities:5
[PlayerManager_430582329311233] Saved prev round data: player:90000022651 entities:5
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000011436 vs Player 90000095932
[OpponentPairManager] Random pair: Player 10102021301 vs Player 90000022651
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_430582329311233] Created 4 opponent pairs
[PlayerManager_430582329311233] Set player opponents, count: 4
[BattleInstanceManager] Created instance 430582329311233_1 for active players 90000011436 vs 90000095932
[CheckerBoard_430582329311233] Cleared checkerboard
[CheckerBoard_430582329311233] Initialized
[BattleInstance] 430582329311233_1 created with players: 90000011436, 90000095932
[BattleInstanceManager] Created instance 430582329311233_2 for active players 10102021301 vs 90000022651
[CheckerBoard_430582329311233] Cleared checkerboard
[CheckerBoard_430582329311233] Initialized
[BattleInstance] 430582329311233_2 created with players: 10102021301, 90000022651
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_430582329311233] Restoring player 10102021301 to My area (rows 1-5)
[CheckerBoard_430582329311233] Placed entity 1 at position (1, 6)
[CheckerBoard_430582329311233] Created entity ID:1 ConfigID:101 at (1, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 1: (6,6)->(1,6), GridID:36->6
[CheckerBoard_430582329311233] Placed entity 2 at position (3, 4)
[CheckerBoard_430582329311233] Created entity ID:2 ConfigID:101 at (3, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 2: (8,4)->(3,4), GridID:46->16
[CheckerBoard_430582329311233] Placed entity 3 at position (4, 1)
[CheckerBoard_430582329311233] Created entity ID:3 ConfigID:103 at (4, 1) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 3: (9,1)->(4,1), GridID:49->19
[CheckerBoard_430582329311233] Placed entity 4 at position (3, 3)
[CheckerBoard_430582329311233] Created entity ID:4 ConfigID:102 at (3, 3) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 4: (8,3)->(3,3), GridID:45->15
[CheckerBoard_430582329311233] Placed entity 5 at position (4, 6)
[CheckerBoard_430582329311233] Created entity ID:5 ConfigID:102 at (4, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 5: (9,6)->(4,6), GridID:54->24
[AutoChessScene_430582329311233] Restored board: player:10102021301 entities:5
[AutoChessScene_430582329311233] Restoring player 90000095932 to Enemy area (rows 6-10)
[CheckerBoard_430582329311233] Placed entity 1 at position (9, 5)
[CheckerBoard_430582329311233] Created entity ID:1 ConfigID:101 at (9, 5) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 1: (4,5)->(9,5), GridID:23->53
[CheckerBoard_430582329311233] Placed entity 2 at position (9, 3)
[CheckerBoard_430582329311233] Created entity ID:2 ConfigID:103 at (9, 3) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 2: (4,3)->(9,3), GridID:21->51
[CheckerBoard_430582329311233] Placed entity 3 at position (9, 6)
[CheckerBoard_430582329311233] Created entity ID:3 ConfigID:101 at (9, 6) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 3: (4,6)->(9,6), GridID:24->54
[CheckerBoard_430582329311233] Placed entity 4 at position (6, 2)
[CheckerBoard_430582329311233] Created entity ID:4 ConfigID:101 at (6, 2) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 4: (1,2)->(6,2), GridID:2->32
[CheckerBoard_430582329311233] Placed entity 5 at position (7, 4)
[CheckerBoard_430582329311233] Created entity ID:5 ConfigID:101 at (7, 4) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 5: (2,4)->(7,4), GridID:10->40
[AutoChessScene_430582329311233] Restored board: player:90000095932 entities:5
[AutoChessScene_430582329311233] Restoring player 90000011436 to My area (rows 1-5)
[CheckerBoard_430582329311233] Placed entity 6 at position (2, 5)
[CheckerBoard_430582329311233] Created entity ID:6 ConfigID:102 at (2, 5) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 6: (7,5)->(2,5), GridID:41->11
[CheckerBoard_430582329311233] Placed entity 7 at position (5, 4)
[CheckerBoard_430582329311233] Created entity ID:7 ConfigID:103 at (5, 4) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 7: (10,4)->(5,4), GridID:58->28
[CheckerBoard_430582329311233] Placed entity 8 at position (4, 3)
[CheckerBoard_430582329311233] Created entity ID:8 ConfigID:103 at (4, 3) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 8: (9,3)->(4,3), GridID:51->21
[CheckerBoard_430582329311233] Placed entity 9 at position (2, 4)
[CheckerBoard_430582329311233] Created entity ID:9 ConfigID:102 at (2, 4) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 9: (7,4)->(2,4), GridID:40->10
[CheckerBoard_430582329311233] Placed entity 10 at position (5, 6)
[CheckerBoard_430582329311233] Created entity ID:10 ConfigID:103 at (5, 6) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 10: (10,6)->(5,6), GridID:60->30
[AutoChessScene_430582329311233] Restored board: player:90000011436 entities:5
[AutoChessScene_430582329311233] Restoring player 90000022651 to Enemy area (rows 6-10)
[CheckerBoard_430582329311233] Placed entity 6 at position (9, 4)
[CheckerBoard_430582329311233] Created entity ID:6 ConfigID:102 at (9, 4) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 6: (4,4)->(9,4), GridID:22->52
[CheckerBoard_430582329311233] Placed entity 7 at position (10, 2)
[CheckerBoard_430582329311233] Created entity ID:7 ConfigID:103 at (10, 2) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 7: (5,2)->(10,2), GridID:26->56
[CheckerBoard_430582329311233] Placed entity 8 at position (10, 4)
[CheckerBoard_430582329311233] Created entity ID:8 ConfigID:102 at (10, 4) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 8: (5,4)->(10,4), GridID:28->58
[CheckerBoard_430582329311233] Placed entity 9 at position (10, 3)
[CheckerBoard_430582329311233] Created entity ID:9 ConfigID:103 at (10, 3) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 9: (5,3)->(10,3), GridID:27->57
[CheckerBoard_430582329311233] Placed entity 10 at position (7, 3)
[CheckerBoard_430582329311233] Created entity ID:10 ConfigID:101 at (7, 3) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 10: (2,3)->(7,3), GridID:9->39
[AutoChessScene_430582329311233] Restored board: player:90000022651 entities:5
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 10102021301
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 90000095932
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 90000011436
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 90000022651
[PlayerManager_430582329311233] Reset all players ready status
[AutoChessScene_430582329311233] Round started with 2 battle instances
[AutoChessScene_430582329311233] Generating buff options for all players
[BuffManager_430582329311233] Generated 3 buff options for player 10102021301: [110, 105, 102]
[AutoChessScene_430582329311233] Generated 3 buff options for player 10102021301: [110, 105, 102]
[BuffManager_430582329311233] Generated 3 buff options for player 90000095932: [106, 107, 104]
[AutoChessScene_430582329311233] Generated 3 buff options for player 90000095932: [106, 107, 104]
[BuffManager_430582329311233] Generated 3 buff options for player 90000011436: [102, 106, 101]
[AutoChessScene_430582329311233] Generated 3 buff options for player 90000011436: [102, 106, 101]
[BuffManager_430582329311233] Generated 3 buff options for player 90000022651: [101, 106, 107]
[AutoChessScene_430582329311233] Generated 3 buff options for player 90000022651: [101, 106, 107]
[AutoChessScene_430582329311233] Player status: Total=4, Active=4
[AutoChessScene_430582329311233] Player 10102021301: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430582329311233] Player 90000095932: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430582329311233] Player 90000011436: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_430582329311233] Player 90000022651: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_430582329311233] Sending RoundStart notifications to 4 active players...
[AutoChessScene_430582329311233] RoundStart: Player 10102021301 buff options: [110, 105, 102]
[AutoChessScene_430582329311233] RoundStart board data: player:10102021301 heroes:5
[AutoChessScene_430582329311233] RoundStart board data: player:90000022651 heroes:5
[AutoChessScene_430582329311233] Sending RoundStart to Player 10102021301 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_430582329311233] RoundStart: Player 90000095932 buff options: [106, 107, 104]
[AutoChessScene_430582329311233] RoundStart board data: player:90000011436 heroes:5
[AutoChessScene_430582329311233] RoundStart board data: player:90000095932 heroes:5
[AutoChessScene_430582329311233] Sending RoundStart to Player 90000095932 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_430582329311233] RoundStart: Player 90000011436 buff options: [102, 106, 101]
[AutoChessScene_430582329311233] RoundStart board data: player:90000011436 heroes:5
[AutoChessScene_430582329311233] RoundStart board data: player:90000095932 heroes:5
[AutoChessScene_430582329311233] Sending RoundStart to Player 90000011436 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_430582329311233] RoundStart: Player 90000022651 buff options: [101, 106, 107]
[AutoChessScene_430582329311233] RoundStart board data: player:10102021301 heroes:5
[AutoChessScene_430582329311233] RoundStart board data: player:90000022651 heroes:5
[AutoChessScene_430582329311233] Sending RoundStart to Player 90000022651 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_430582329311233] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 430582329311233 state to StateRoundStart
[AutoChessScene_430582329311233] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R2)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleStateManager_430582329311233] ===== ROUND 2 INITIALIZATION COMPLETE =====
[BattleStateManager_430582329311233] Buff selection timer started: 25000ms
[BattleStateManager_430582329311233] State: StateRoundStart -> StatePreparation (R2, 65000ms)
[AutoChessScene_430582329311233] Preparation phase started
[BuffManager_430582329311233] Added buff 106 (Buff_106) to player 90000095932
[AutoChessScene_430582329311233] Auto-selected buff 106 for bot player 90000095932
[CheckerBoard_430582329311233] Placed entity 11 at position (8, 3)
[CheckerBoard_430582329311233] Created entity ID:11 ConfigID:101 at (8, 3) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 12 at position (6, 6)
[CheckerBoard_430582329311233] Created entity ID:12 ConfigID:101 at (6, 6) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 13 at position (6, 5)
[CheckerBoard_430582329311233] Created entity ID:13 ConfigID:103 at (6, 5) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 14 at position (10, 1)
[CheckerBoard_430582329311233] Created entity ID:14 ConfigID:101 at (10, 1) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 15 at position (7, 3)
[CheckerBoard_430582329311233] Created entity ID:15 ConfigID:101 at (7, 3) for player 90000095932
[CheckerBoard_430582329311233] CheckTimes limit (4) reached for 1 hero types for player 90000095932
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 90000095932: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 90000095932 in Enemy area
[AutoChessScene_430582329311233] Generated 5 heroes for player 90000095932: 5 placed on board, 0 in temporary slots
[AutoChessScene_430582329311233] Generated 5 new heroes for bot player 90000095932 after buff selection
[BuffManager_430582329311233] Added buff 102 (Buff_102) to player 90000011436
[AutoChessScene_430582329311233] Auto-selected buff 102 for bot player 90000011436
[CheckerBoard_430582329311233] Placed entity 16 at position (3, 5)
[CheckerBoard_430582329311233] Created entity ID:16 ConfigID:101 at (3, 5) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 17 at position (5, 1)
[CheckerBoard_430582329311233] Created entity ID:17 ConfigID:102 at (5, 1) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 18 at position (1, 2)
[CheckerBoard_430582329311233] Created entity ID:18 ConfigID:101 at (1, 2) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 19 at position (4, 4)
[CheckerBoard_430582329311233] Created entity ID:19 ConfigID:101 at (4, 4) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 20 at position (5, 5)
[CheckerBoard_430582329311233] Created entity ID:20 ConfigID:103 at (5, 5) for player 90000011436
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 90000011436: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 90000011436 in My area
[AutoChessScene_430582329311233] Generated 5 heroes for player 90000011436: 5 placed on board, 0 in temporary slots
[AutoChessScene_430582329311233] Generated 5 new heroes for bot player 90000011436 after buff selection
[BuffManager_430582329311233] Added buff 101 (Buff_101) to player 90000022651
[AutoChessScene_430582329311233] Auto-selected buff 101 for bot player 90000022651
[CheckerBoard_430582329311233] Placed entity 11 at position (7, 2)
[CheckerBoard_430582329311233] Created entity ID:11 ConfigID:102 at (7, 2) for player 90000022651
[CheckerBoard_430582329311233] Placed entity 12 at position (7, 4)
[CheckerBoard_430582329311233] Created entity ID:12 ConfigID:102 at (7, 4) for player 90000022651
[CheckerBoard_430582329311233] Placed entity 13 at position (10, 6)
[CheckerBoard_430582329311233] Created entity ID:13 ConfigID:102 at (10, 6) for player 90000022651
[CheckerBoard_430582329311233] Placed entity 14 at position (7, 6)
[CheckerBoard_430582329311233] Created entity ID:14 ConfigID:102 at (7, 6) for player 90000022651
[CheckerBoard_430582329311233] Placed entity 15 at position (10, 1)
[CheckerBoard_430582329311233] Created entity ID:15 ConfigID:103 at (10, 1) for player 90000022651
[CheckerBoard_430582329311233] CheckTimes limit (4) reached for 1 hero types for player 90000022651
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 90000022651: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 90000022651 in Enemy area
[AutoChessScene_430582329311233] Generated 5 heroes for player 90000022651: 5 placed on board, 0 in temporary slots
[AutoChessScene_430582329311233] Generated 5 new heroes for bot player 90000022651 after buff selection
[PlayerManager_430582329311233] Player 90000095932 ready status set to True
[PlayerManager_430582329311233] Player 90000011436 ready status set to True
[PlayerManager_430582329311233] Player 90000022651 ready status set to True
[AutoChessScene_430582329311233] Auto-ready 3 additional bots
[AutoChessScene_430582329311233] Free operation phase started
[BattleService] Updated battle 430582329311233 state to StatePreparation
[AutoChessScene_430582329311233] State change sent to GameServer: StateRoundStart -> StatePreparation (R2)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED SelectBuffer RPC =====
[BattleService] Player 10102021301 is selecting buff 105
[BuffManager_430582329311233] Added buff 105 (Buff_105) to player 10102021301
[CheckerBoard_430582329311233] Placed entity 16 at position (2, 4)
[CheckerBoard_430582329311233] Created entity ID:16 ConfigID:103 at (2, 4) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 17 at position (2, 3)
[CheckerBoard_430582329311233] Created entity ID:17 ConfigID:101 at (2, 3) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 18 at position (5, 6)
[CheckerBoard_430582329311233] Created entity ID:18 ConfigID:101 at (5, 6) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 19 at position (3, 6)
[CheckerBoard_430582329311233] Created entity ID:19 ConfigID:103 at (3, 6) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 20 at position (1, 1)
[CheckerBoard_430582329311233] Created entity ID:20 ConfigID:103 at (1, 1) for player 10102021301
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 10102021301: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 10102021301 in My area
[AutoChessScene_430582329311233] Generated 5 heroes for player 10102021301: 5 placed on board, 0 in temporary slots
[AutoChessScene_430582329311233] Player 10102021301 selected buff 105, generated 5 new heroes
[BattleService] SelectBuffer response: Code=0, NewHeroes=5
[PlayerManager_430582329311233] Player 10102021301 ready status set to True
[PlayerManager_430582329311233] All players are ready!
[AutoChessScene_430582329311233] All players are ready, transitioning to next state
[BattleStateManager_430582329311233] State: StatePreparation -> StateBattleStarting (R2, 1000ms)
[AutoChessScene_430582329311233] Applying battle start buffs for all players
[BuffManager_430582329311233] Applying battle start buff 105 (Buff_105) for player 10102021301
[BuffManager_430582329311233] Applying battle start buff 106 (Buff_106) for player 90000095932
[BuffManager_430582329311233] Applying battle start buff 102 (Buff_102) for player 90000011436
[BuffManager_430582329311233] Applying battle start buff 101 (Buff_101) for player 90000022651
[AutoChessScene_430582329311233] Camp info for player 10102021301: 10 heroes added
[AutoChessScene_430582329311233] Camp info for player 90000022651: 10 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 10102021301, Team order: [10102021301, 90000022651], total GridIDs used: 20
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 10102021301 vs Opponent 90000022651 with 2 teams
[AutoChessScene_430582329311233] Camp info for player 90000011436: 10 heroes added
[AutoChessScene_430582329311233] Camp info for player 90000095932: 10 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 90000095932, Team order: [90000011436, 90000095932], total GridIDs used: 20
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 90000095932 vs Opponent 90000011436 with 2 teams
[AutoChessScene_430582329311233] Camp info for player 90000011436: 10 heroes added
[AutoChessScene_430582329311233] Camp info for player 90000095932: 10 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 90000011436, Team order: [90000011436, 90000095932], total GridIDs used: 20
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 90000011436 vs Opponent 90000095932 with 2 teams
[AutoChessScene_430582329311233] Camp info for player 10102021301: 10 heroes added
[AutoChessScene_430582329311233] Camp info for player 90000022651: 10 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 90000022651, Team order: [10102021301, 90000022651], total GridIDs used: 20
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 90000022651 vs Opponent 10102021301 with 2 teams
[AutoChessScene_430582329311233] Sent RoundBattleStart notifications with seed: 1443760286
[BattleService] Updated battle 430582329311233 state to StateBattleStarting
[AutoChessScene_430582329311233] State change sent to GameServer: StatePreparation -> StateBattleStarting (R2)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[AutoChessScene_430582329311233] Player 10102021301 set ready status to True
[BattleStateManager_430582329311233] State: StateBattleStarting -> StateBattleInProgress (R2, 65000ms)
[AutoChessScene_430582329311233] Starting all battle instances
[BattleInstance] 430582329311233_1 battle started
[BattleInstance] 430582329311233_2 battle started
[BattleInstanceManager] Started all 2 battle instances
[AutoChessScene_430582329311233] Auto EndBattle for bot 90000095932 vs bot 90000011436, random result: bot 90000095932 wins = True
[AutoChessScene_430582329311233] Player 90000095932 sent EndBattleReq (win: True), instance: 430582329311233_1
[AutoChessScene_430582329311233] Waiting for opponent 90000011436 to send EndBattleReq for instance 430582329311233_1
[AutoChessScene_430582329311233] Player 90000011436 sent EndBattleReq (win: False), instance: 430582329311233_1
[BattleInstance] 430582329311233_1 battle finished, winner: 90000095932, loser: 90000011436
[AutoChessScene_430582329311233] Battle instance 430582329311233_1 completed: Winner 90000095932, Loser 90000011436
[AutoChessScene_430582329311233] Bot 90000022651 vs real player 10102021301, waiting for real player result
[AutoChessScene_430582329311233] Bot vs real player battles will be handled by system timeout (62s)
[BattleService] Updated battle 430582329311233 state to StateBattleInProgress
[AutoChessScene_430582329311233] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R2)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] EndBattle uid: 10102021301, win: True
[AutoChessScene_430582329311233] Player 10102021301 sent EndBattleReq (win: True), instance: 430582329311233_2
[AutoChessScene_430582329311233] Auto EndBattle for bot 90000022651 vs real player 10102021301, bot result: False
[BattleInstance] 430582329311233_2 battle finished, winner: 10102021301, loser: 90000022651
[AutoChessScene_430582329311233] Battle instance 430582329311233_2 completed: Winner 10102021301, Loser 90000022651
[AutoChessScene_430582329311233] All battle instances finished, proceeding to settlement
[BattleStateManager_430582329311233] State: StateBattleInProgress -> StateRoundSettlement (R2, 5000ms)
[AutoChessScene_430582329311233] Processing battle results
[PlayerManager_430582329311233] Player 90000011436 health reduced by 1, current health: 1
[AutoChessScene_430582329311233] Player 90000011436 lost 1 health, winner: 90000095932
[AutoChessScene_430582329311233] Sent RoundBattleEnd notification (isEnd=false) - Winner: 90000095932, Loser: 90000011436
[PlayerManager_430582329311233] Player 90000022651 health reduced by 1, current health: 1
[AutoChessScene_430582329311233] Player 90000022651 lost 1 health, winner: 10102021301
[AutoChessScene_430582329311233] Sent RoundBattleEnd notification (isEnd=false) - Winner: 10102021301, Loser: 90000022651
[BattleStateManager_430582329311233] Invalid state transition from StateRoundSettlement to StateRoundSettlement
[AutoChessScene_430582329311233] Checking players elimination
[AutoChessScene_430582329311233] Active players remaining: 4
[AutoChessScene_430582329311233] Waiting for all players to confirm round settlement before starting new round
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000095932
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000011436
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000022651
[BattleService] Updated battle 430582329311233 state to StateRoundSettlement
[AutoChessScene_430582329311233] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R2)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10102021301 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10102021301 confirmed round settlement, current state: StateRoundSettlement
[AutoChessScene_430582329311233] Player 10102021301 confirmed round settlement, count: 4
[AutoChessScene_430582329311233] Real player 10102021301 confirmed, auto-confirming all bots
[AutoChessScene_430582329311233] All players confirmed round settlement, starting new round
[PlayerManager_430582329311233] Player 10102021301 ready status set to False
[PlayerManager_430582329311233] Player 90000095932 ready status set to False
[PlayerManager_430582329311233] Player 90000011436 ready status set to False
[PlayerManager_430582329311233] Player 90000022651 ready status set to False
[BattleStateManager_430582329311233] ===== STARTING NEW ROUND 3 =====
[BattleStateManager_430582329311233] Round 3 has buff selection: False
[BattleStateManager_430582329311233] Publishing RoundStartedEvent for round 3
[AutoChessScene_430582329311233] Round 3 started
[BattleStateManager_430582329311233] Setting state to StateRoundStart for round 3
[BattleStateManager_430582329311233] State: StateRoundSettlement -> StateRoundStart (R3, 1000ms)
[AutoChessScene_430582329311233] HandleRoundStart: 4 active players
[AutoChessScene_430582329311233] Valid player: 10102021301, Health: 3
[AutoChessScene_430582329311233] Valid player: 90000095932, Health: 3
[AutoChessScene_430582329311233] Valid player: 90000011436, Health: 1
[AutoChessScene_430582329311233] Valid player: 90000022651, Health: 1
[AutoChessScene_430582329311233] Player 10102021301 has 10 entities to save
[PlayerManager_430582329311233] Saved board data: player:10102021301 entities:10
[PlayerManager_430582329311233] Saved prev round data: player:10102021301 entities:10
[AutoChessScene_430582329311233] Player 90000095932 has 10 entities to save
[PlayerManager_430582329311233] Saved board data: player:90000095932 entities:10
[PlayerManager_430582329311233] Saved prev round data: player:90000095932 entities:10
[AutoChessScene_430582329311233] Player 90000011436 has 10 entities to save
[PlayerManager_430582329311233] Saved board data: player:90000011436 entities:10
[PlayerManager_430582329311233] Saved prev round data: player:90000011436 entities:10
[AutoChessScene_430582329311233] Player 90000022651 has 10 entities to save
[PlayerManager_430582329311233] Saved board data: player:90000022651 entities:10
[PlayerManager_430582329311233] Saved prev round data: player:90000022651 entities:10
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000011436 vs Player 90000095932
[OpponentPairManager] Random pair: Player 90000022651 vs Player 10102021301
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_430582329311233] Created 4 opponent pairs
[PlayerManager_430582329311233] Set player opponents, count: 4
[BattleInstanceManager] Created instance 430582329311233_1 for active players 90000011436 vs 90000095932
[CheckerBoard_430582329311233] Cleared checkerboard
[CheckerBoard_430582329311233] Initialized
[BattleInstance] 430582329311233_1 created with players: 90000011436, 90000095932
[BattleInstanceManager] Created instance 430582329311233_2 for active players 90000022651 vs 10102021301
[CheckerBoard_430582329311233] Cleared checkerboard
[CheckerBoard_430582329311233] Initialized
[BattleInstance] 430582329311233_2 created with players: 90000022651, 10102021301
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_430582329311233] Restoring player 10102021301 to Enemy area (rows 6-10)
[CheckerBoard_430582329311233] Placed entity 1 at position (6, 6)
[CheckerBoard_430582329311233] Created entity ID:1 ConfigID:101 at (6, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 1: (1,6)->(6,6), GridID:6->36
[CheckerBoard_430582329311233] Placed entity 2 at position (8, 4)
[CheckerBoard_430582329311233] Created entity ID:2 ConfigID:101 at (8, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 2: (3,4)->(8,4), GridID:16->46
[CheckerBoard_430582329311233] Placed entity 3 at position (9, 1)
[CheckerBoard_430582329311233] Created entity ID:3 ConfigID:103 at (9, 1) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 3: (4,1)->(9,1), GridID:19->49
[CheckerBoard_430582329311233] Placed entity 4 at position (8, 3)
[CheckerBoard_430582329311233] Created entity ID:4 ConfigID:102 at (8, 3) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 4: (3,3)->(8,3), GridID:15->45
[CheckerBoard_430582329311233] Placed entity 5 at position (9, 6)
[CheckerBoard_430582329311233] Created entity ID:5 ConfigID:102 at (9, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 5: (4,6)->(9,6), GridID:24->54
[CheckerBoard_430582329311233] Placed entity 6 at position (7, 4)
[CheckerBoard_430582329311233] Created entity ID:6 ConfigID:103 at (7, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 6: (2,4)->(7,4), GridID:10->40
[CheckerBoard_430582329311233] Placed entity 7 at position (7, 3)
[CheckerBoard_430582329311233] Created entity ID:7 ConfigID:101 at (7, 3) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 7: (2,3)->(7,3), GridID:9->39
[CheckerBoard_430582329311233] Placed entity 8 at position (10, 6)
[CheckerBoard_430582329311233] Created entity ID:8 ConfigID:101 at (10, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 8: (5,6)->(10,6), GridID:30->60
[CheckerBoard_430582329311233] Placed entity 9 at position (8, 6)
[CheckerBoard_430582329311233] Created entity ID:9 ConfigID:103 at (8, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 9: (3,6)->(8,6), GridID:18->48
[CheckerBoard_430582329311233] Placed entity 10 at position (6, 1)
[CheckerBoard_430582329311233] Created entity ID:10 ConfigID:103 at (6, 1) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 10: (1,1)->(6,1), GridID:1->31
[AutoChessScene_430582329311233] Restored board: player:10102021301 entities:10
[AutoChessScene_430582329311233] Restoring player 90000095932 to Enemy area (rows 6-10)
[CheckerBoard_430582329311233] Placed entity 1 at position (9, 5)
[CheckerBoard_430582329311233] Created entity ID:1 ConfigID:101 at (9, 5) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 1: (9,5)->(9,5), GridID:53->53
[CheckerBoard_430582329311233] Placed entity 2 at position (9, 3)
[CheckerBoard_430582329311233] Created entity ID:2 ConfigID:103 at (9, 3) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 2: (9,3)->(9,3), GridID:51->51
[CheckerBoard_430582329311233] Placed entity 3 at position (9, 6)
[CheckerBoard_430582329311233] Created entity ID:3 ConfigID:101 at (9, 6) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 3: (9,6)->(9,6), GridID:54->54
[CheckerBoard_430582329311233] Placed entity 4 at position (6, 2)
[CheckerBoard_430582329311233] Created entity ID:4 ConfigID:101 at (6, 2) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 4: (6,2)->(6,2), GridID:32->32
[CheckerBoard_430582329311233] Placed entity 5 at position (7, 4)
[CheckerBoard_430582329311233] Created entity ID:5 ConfigID:101 at (7, 4) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 5: (7,4)->(7,4), GridID:40->40
[CheckerBoard_430582329311233] Placed entity 6 at position (8, 3)
[CheckerBoard_430582329311233] Created entity ID:6 ConfigID:101 at (8, 3) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 6: (8,3)->(8,3), GridID:45->45
[CheckerBoard_430582329311233] Placed entity 7 at position (6, 6)
[CheckerBoard_430582329311233] Created entity ID:7 ConfigID:101 at (6, 6) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 7: (6,6)->(6,6), GridID:36->36
[CheckerBoard_430582329311233] Placed entity 8 at position (6, 5)
[CheckerBoard_430582329311233] Created entity ID:8 ConfigID:103 at (6, 5) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 8: (6,5)->(6,5), GridID:35->35
[CheckerBoard_430582329311233] Placed entity 9 at position (10, 1)
[CheckerBoard_430582329311233] Created entity ID:9 ConfigID:101 at (10, 1) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 9: (10,1)->(10,1), GridID:55->55
[CheckerBoard_430582329311233] Placed entity 10 at position (7, 3)
[CheckerBoard_430582329311233] Created entity ID:10 ConfigID:101 at (7, 3) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 10: (7,3)->(7,3), GridID:39->39
[AutoChessScene_430582329311233] Restored board: player:90000095932 entities:10
[AutoChessScene_430582329311233] Restoring player 90000011436 to My area (rows 1-5)
[CheckerBoard_430582329311233] Placed entity 11 at position (2, 5)
[CheckerBoard_430582329311233] Created entity ID:11 ConfigID:102 at (2, 5) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 11: (2,5)->(2,5), GridID:11->11
[CheckerBoard_430582329311233] Placed entity 12 at position (5, 4)
[CheckerBoard_430582329311233] Created entity ID:12 ConfigID:103 at (5, 4) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 12: (5,4)->(5,4), GridID:28->28
[CheckerBoard_430582329311233] Placed entity 13 at position (4, 3)
[CheckerBoard_430582329311233] Created entity ID:13 ConfigID:103 at (4, 3) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 13: (4,3)->(4,3), GridID:21->21
[CheckerBoard_430582329311233] Placed entity 14 at position (2, 4)
[CheckerBoard_430582329311233] Created entity ID:14 ConfigID:102 at (2, 4) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 14: (2,4)->(2,4), GridID:10->10
[CheckerBoard_430582329311233] Placed entity 15 at position (5, 6)
[CheckerBoard_430582329311233] Created entity ID:15 ConfigID:103 at (5, 6) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 15: (5,6)->(5,6), GridID:30->30
[CheckerBoard_430582329311233] Placed entity 16 at position (3, 5)
[CheckerBoard_430582329311233] Created entity ID:16 ConfigID:101 at (3, 5) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 16: (3,5)->(3,5), GridID:17->17
[CheckerBoard_430582329311233] Placed entity 17 at position (5, 1)
[CheckerBoard_430582329311233] Created entity ID:17 ConfigID:102 at (5, 1) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 17: (5,1)->(5,1), GridID:25->25
[CheckerBoard_430582329311233] Placed entity 18 at position (1, 2)
[CheckerBoard_430582329311233] Created entity ID:18 ConfigID:101 at (1, 2) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 18: (1,2)->(1,2), GridID:2->2
[CheckerBoard_430582329311233] Placed entity 19 at position (4, 4)
[CheckerBoard_430582329311233] Created entity ID:19 ConfigID:101 at (4, 4) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 19: (4,4)->(4,4), GridID:22->22
[CheckerBoard_430582329311233] Placed entity 20 at position (5, 5)
[CheckerBoard_430582329311233] Created entity ID:20 ConfigID:103 at (5, 5) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 20: (5,5)->(5,5), GridID:29->29
[AutoChessScene_430582329311233] Restored board: player:90000011436 entities:10
[AutoChessScene_430582329311233] Restoring player 90000022651 to My area (rows 1-5)
[CheckerBoard_430582329311233] Placed entity 11 at position (4, 4)
[CheckerBoard_430582329311233] Created entity ID:11 ConfigID:102 at (4, 4) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 11: (9,4)->(4,4), GridID:52->22
[CheckerBoard_430582329311233] Placed entity 12 at position (5, 2)
[CheckerBoard_430582329311233] Created entity ID:12 ConfigID:103 at (5, 2) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 12: (10,2)->(5,2), GridID:56->26
[CheckerBoard_430582329311233] Placed entity 13 at position (5, 4)
[CheckerBoard_430582329311233] Created entity ID:13 ConfigID:102 at (5, 4) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 13: (10,4)->(5,4), GridID:58->28
[CheckerBoard_430582329311233] Placed entity 14 at position (5, 3)
[CheckerBoard_430582329311233] Created entity ID:14 ConfigID:103 at (5, 3) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 14: (10,3)->(5,3), GridID:57->27
[CheckerBoard_430582329311233] Placed entity 15 at position (2, 3)
[CheckerBoard_430582329311233] Created entity ID:15 ConfigID:101 at (2, 3) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 15: (7,3)->(2,3), GridID:39->9
[CheckerBoard_430582329311233] Placed entity 16 at position (2, 2)
[CheckerBoard_430582329311233] Created entity ID:16 ConfigID:102 at (2, 2) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 16: (7,2)->(2,2), GridID:38->8
[CheckerBoard_430582329311233] Placed entity 17 at position (2, 4)
[CheckerBoard_430582329311233] Created entity ID:17 ConfigID:102 at (2, 4) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 17: (7,4)->(2,4), GridID:40->10
[CheckerBoard_430582329311233] Placed entity 18 at position (5, 6)
[CheckerBoard_430582329311233] Created entity ID:18 ConfigID:102 at (5, 6) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 18: (10,6)->(5,6), GridID:60->30
[CheckerBoard_430582329311233] Placed entity 19 at position (2, 6)
[CheckerBoard_430582329311233] Created entity ID:19 ConfigID:102 at (2, 6) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 19: (7,6)->(2,6), GridID:42->12
[CheckerBoard_430582329311233] Placed entity 20 at position (5, 1)
[CheckerBoard_430582329311233] Created entity ID:20 ConfigID:103 at (5, 1) for player 90000022651
[AutoChessScene_430582329311233] Restored entity 20: (10,1)->(5,1), GridID:55->25
[AutoChessScene_430582329311233] Restored board: player:90000022651 entities:10
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 10102021301
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 90000095932
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 90000011436
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 90000022651
[PlayerManager_430582329311233] Reset all players ready status
[AutoChessScene_430582329311233] Round started with 2 battle instances
[AutoChessScene_430582329311233] Generating 5 heroes for all players in round 3
[CheckerBoard_430582329311233] Placed entity 21 at position (9, 3)
[CheckerBoard_430582329311233] Created entity ID:21 ConfigID:103 at (9, 3) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 22 at position (8, 2)
[CheckerBoard_430582329311233] Created entity ID:22 ConfigID:102 at (8, 2) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 23 at position (10, 5)
[CheckerBoard_430582329311233] Created entity ID:23 ConfigID:101 at (10, 5) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 24 at position (8, 5)
[CheckerBoard_430582329311233] Created entity ID:24 ConfigID:101 at (8, 5) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 25 at position (6, 4)
[CheckerBoard_430582329311233] Created entity ID:25 ConfigID:101 at (6, 4) for player 10102021301
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 10102021301: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 10102021301 in Enemy area
[AutoChessScene_430582329311233] Generated 5 heroes for player 10102021301: 5 placed on board, 0 in temporary slots
[CheckerBoard_430582329311233] Placed entity 21 at position (9, 2)
[CheckerBoard_430582329311233] Created entity ID:21 ConfigID:102 at (9, 2) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 22 at position (8, 6)
[CheckerBoard_430582329311233] Created entity ID:22 ConfigID:101 at (8, 6) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 23 at position (10, 2)
[CheckerBoard_430582329311233] Created entity ID:23 ConfigID:103 at (10, 2) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 24 at position (9, 1)
[CheckerBoard_430582329311233] Created entity ID:24 ConfigID:102 at (9, 1) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 25 at position (8, 2)
[CheckerBoard_430582329311233] Created entity ID:25 ConfigID:101 at (8, 2) for player 90000095932
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 90000095932: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 90000095932 in Enemy area
[AutoChessScene_430582329311233] Generated 5 heroes for player 90000095932: 5 placed on board, 0 in temporary slots
[CheckerBoard_430582329311233] Placed entity 26 at position (1, 5)
[CheckerBoard_430582329311233] Created entity ID:26 ConfigID:103 at (1, 5) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 27 at position (3, 1)
[CheckerBoard_430582329311233] Created entity ID:27 ConfigID:102 at (3, 1) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 28 at position (1, 3)
[CheckerBoard_430582329311233] Created entity ID:28 ConfigID:101 at (1, 3) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 29 at position (3, 2)
[CheckerBoard_430582329311233] Created entity ID:29 ConfigID:103 at (3, 2) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 30 at position (1, 1)
[CheckerBoard_430582329311233] Created entity ID:30 ConfigID:102 at (1, 1) for player 90000011436
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 90000011436: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 90000011436 in My area
[AutoChessScene_430582329311233] Generated 5 heroes for player 90000011436: 5 placed on board, 0 in temporary slots
[CheckerBoard_430582329311233] Placed entity 26 at position (3, 4)
[CheckerBoard_430582329311233] Created entity ID:26 ConfigID:103 at (3, 4) for player 90000022651
[CheckerBoard_430582329311233] Placed entity 27 at position (4, 2)
[CheckerBoard_430582329311233] Created entity ID:27 ConfigID:101 at (4, 2) for player 90000022651
[CheckerBoard_430582329311233] Placed entity 28 at position (3, 3)
[CheckerBoard_430582329311233] Created entity ID:28 ConfigID:103 at (3, 3) for player 90000022651
[CheckerBoard_430582329311233] Placed entity 29 at position (1, 2)
[CheckerBoard_430582329311233] Created entity ID:29 ConfigID:102 at (1, 2) for player 90000022651
[CheckerBoard_430582329311233] Placed entity 30 at position (3, 5)
[CheckerBoard_430582329311233] Created entity ID:30 ConfigID:102 at (3, 5) for player 90000022651
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 90000022651: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 90000022651 in My area
[AutoChessScene_430582329311233] Generated 5 heroes for player 90000022651: 5 placed on board, 0 in temporary slots
[AutoChessScene_430582329311233] Player status: Total=4, Active=4
[AutoChessScene_430582329311233] Player 10102021301: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430582329311233] Player 90000095932: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430582329311233] Player 90000011436: Eliminated=False, Health=1, HasInstance=True
[AutoChessScene_430582329311233] Player 90000022651: Eliminated=False, Health=1, HasInstance=True
[AutoChessScene_430582329311233] Sending RoundStart notifications to 4 active players...
[AutoChessScene_430582329311233] RoundStart board data: player:90000022651 heroes:15
[AutoChessScene_430582329311233] RoundStart board data: player:10102021301 heroes:15
[AutoChessScene_430582329311233] Sending RoundStart to Player 10102021301 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430582329311233] RoundStart board data: player:90000011436 heroes:15
[AutoChessScene_430582329311233] RoundStart board data: player:90000095932 heroes:15
[AutoChessScene_430582329311233] Sending RoundStart to Player 90000095932 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430582329311233] RoundStart board data: player:90000011436 heroes:15
[AutoChessScene_430582329311233] RoundStart board data: player:90000095932 heroes:15
[AutoChessScene_430582329311233] Sending RoundStart to Player 90000011436 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430582329311233] RoundStart board data: player:90000022651 heroes:15
[AutoChessScene_430582329311233] RoundStart board data: player:10102021301 heroes:15
[AutoChessScene_430582329311233] Sending RoundStart to Player 90000022651 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430582329311233] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 430582329311233 state to StateRoundStart
[AutoChessScene_430582329311233] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R3)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleStateManager_430582329311233] ===== ROUND 3 INITIALIZATION COMPLETE =====
[BattleStateManager_430582329311233] State: StateRoundStart -> StatePreparation (R3, 65000ms)
[AutoChessScene_430582329311233] Preparation phase started
[PlayerManager_430582329311233] Player 90000095932 ready status set to True
[PlayerManager_430582329311233] Player 90000011436 ready status set to True
[PlayerManager_430582329311233] Player 90000022651 ready status set to True
[AutoChessScene_430582329311233] Auto-ready 3 additional bots
[AutoChessScene_430582329311233] Free operation phase started
[BattleService] Updated battle 430582329311233 state to StatePreparation
[AutoChessScene_430582329311233] State change sent to GameServer: StateRoundStart -> StatePreparation (R3)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[PlayerManager_430582329311233] Player 10102021301 ready status set to True
[PlayerManager_430582329311233] All players are ready!
[AutoChessScene_430582329311233] All players are ready, transitioning to next state
[BattleStateManager_430582329311233] State: StatePreparation -> StateBattleStarting (R3, 1000ms)
[AutoChessScene_430582329311233] Applying battle start buffs for all players
[BuffManager_430582329311233] Applying battle start buff 105 (Buff_105) for player 10102021301
[BuffManager_430582329311233] Applying battle start buff 106 (Buff_106) for player 90000095932
[BuffManager_430582329311233] Applying battle start buff 102 (Buff_102) for player 90000011436
[BuffManager_430582329311233] Applying battle start buff 101 (Buff_101) for player 90000022651
[AutoChessScene_430582329311233] Camp info for player 90000022651: 15 heroes added
[AutoChessScene_430582329311233] Camp info for player 10102021301: 15 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 10102021301, Team order: [90000022651, 10102021301], total GridIDs used: 30
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 10102021301 vs Opponent 90000022651 with 2 teams
[AutoChessScene_430582329311233] Camp info for player 90000011436: 15 heroes added
[AutoChessScene_430582329311233] Camp info for player 90000095932: 15 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 90000095932, Team order: [90000011436, 90000095932], total GridIDs used: 30
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 90000095932 vs Opponent 90000011436 with 2 teams
[AutoChessScene_430582329311233] Camp info for player 90000011436: 15 heroes added
[AutoChessScene_430582329311233] Camp info for player 90000095932: 15 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 90000011436, Team order: [90000011436, 90000095932], total GridIDs used: 30
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 90000011436 vs Opponent 90000095932 with 2 teams
[AutoChessScene_430582329311233] Camp info for player 90000022651: 15 heroes added
[AutoChessScene_430582329311233] Camp info for player 10102021301: 15 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 90000022651, Team order: [90000022651, 10102021301], total GridIDs used: 30
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 90000022651 vs Opponent 10102021301 with 2 teams
[AutoChessScene_430582329311233] Sent RoundBattleStart notifications with seed: 31971645
[BattleService] Updated battle 430582329311233 state to StateBattleStarting
[AutoChessScene_430582329311233] State change sent to GameServer: StatePreparation -> StateBattleStarting (R3)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[AutoChessScene_430582329311233] Player 10102021301 set ready status to True
[BattleStateManager_430582329311233] State: StateBattleStarting -> StateBattleInProgress (R3, 65000ms)
[AutoChessScene_430582329311233] Starting all battle instances
[BattleInstance] 430582329311233_1 battle started
[BattleInstance] 430582329311233_2 battle started
[BattleInstanceManager] Started all 2 battle instances
[AutoChessScene_430582329311233] Auto EndBattle for bot 90000095932 vs bot 90000011436, random result: bot 90000095932 wins = False
[AutoChessScene_430582329311233] Player 90000095932 sent EndBattleReq (win: False), instance: 430582329311233_1
[AutoChessScene_430582329311233] Waiting for opponent 90000011436 to send EndBattleReq for instance 430582329311233_1
[AutoChessScene_430582329311233] Player 90000011436 sent EndBattleReq (win: True), instance: 430582329311233_1
[BattleInstance] 430582329311233_1 battle finished, winner: 90000011436, loser: 90000095932
[AutoChessScene_430582329311233] Battle instance 430582329311233_1 completed: Winner 90000011436, Loser 90000095932
[AutoChessScene_430582329311233] Bot 90000022651 vs real player 10102021301, waiting for real player result
[AutoChessScene_430582329311233] Bot vs real player battles will be handled by system timeout (62s)
[BattleService] Updated battle 430582329311233 state to StateBattleInProgress
[AutoChessScene_430582329311233] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R3)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] EndBattle uid: 10102021301, win: True
[AutoChessScene_430582329311233] Player 10102021301 sent EndBattleReq (win: True), instance: 430582329311233_2
[AutoChessScene_430582329311233] Auto EndBattle for bot 90000022651 vs real player 10102021301, bot result: False
[BattleInstance] 430582329311233_2 battle finished, winner: 10102021301, loser: 90000022651
[AutoChessScene_430582329311233] Battle instance 430582329311233_2 completed: Winner 10102021301, Loser 90000022651
[AutoChessScene_430582329311233] All battle instances finished, proceeding to settlement
[BattleStateManager_430582329311233] State: StateBattleInProgress -> StateRoundSettlement (R3, 5000ms)
[AutoChessScene_430582329311233] Processing battle results
[PlayerManager_430582329311233] Player 90000095932 health reduced by 1, current health: 2
[AutoChessScene_430582329311233] Player 90000095932 lost 1 health, winner: 90000011436
[AutoChessScene_430582329311233] Sent RoundBattleEnd notification (isEnd=false) - Winner: 90000011436, Loser: 90000095932
[PlayerManager_430582329311233] Player 90000022651 health reduced by 1, current health: 0
[PlayerManager_430582329311233] Player 90000022651 eliminated from battle 430582329311233
[AutoChessScene_430582329311233] Player 90000022651 has been eliminated
[AutoChessScene_430582329311233] Cleared 0 entities for player 90000022651
[AutoChessScene_430582329311233] Player 90000022651 lost 1 health, winner: 10102021301
[AutoChessScene_430582329311233] Sent RoundBattleEnd notification (isEnd=false) - Winner: 10102021301, Loser: 90000022651
[BattleStateManager_430582329311233] Invalid state transition from StateRoundSettlement to StateRoundSettlement
[AutoChessScene_430582329311233] Checking players elimination
[AutoChessScene_430582329311233] Active players remaining: 3
[AutoChessScene_430582329311233] Waiting for all players to confirm round settlement before starting new round
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000095932
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000011436
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000022651
[BattleService] Updated battle 430582329311233 state to StateRoundSettlement
[AutoChessScene_430582329311233] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R3)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10102021301 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10102021301 confirmed round settlement, current state: StateRoundSettlement
[AutoChessScene_430582329311233] Player 10102021301 confirmed round settlement, count: 4
[AutoChessScene_430582329311233] Real player 10102021301 confirmed, auto-confirming all bots
[AutoChessScene_430582329311233] All players confirmed round settlement, starting new round
[PlayerManager_430582329311233] Player 10102021301 ready status set to False
[PlayerManager_430582329311233] Player 90000095932 ready status set to False
[PlayerManager_430582329311233] Player 90000011436 ready status set to False
[BattleStateManager_430582329311233] ===== STARTING NEW ROUND 4 =====
[BattleStateManager_430582329311233] Round 4 has buff selection: True
[BattleStateManager_430582329311233] Publishing RoundStartedEvent for round 4
[AutoChessScene_430582329311233] Round 4 started
[BattleStateManager_430582329311233] Setting state to StateRoundStart for round 4
[BattleStateManager_430582329311233] State: StateRoundSettlement -> StateRoundStart (R4, 1000ms)
[AutoChessScene_430582329311233] HandleRoundStart: 3 active players
[AutoChessScene_430582329311233] Valid player: 10102021301, Health: 3
[AutoChessScene_430582329311233] Valid player: 90000095932, Health: 2
[AutoChessScene_430582329311233] Valid player: 90000011436, Health: 1
[AutoChessScene_430582329311233] Player 10102021301 has 15 entities to save
[PlayerManager_430582329311233] Saved board data: player:10102021301 entities:15
[PlayerManager_430582329311233] Saved prev round data: player:10102021301 entities:15
[AutoChessScene_430582329311233] Player 90000095932 has 15 entities to save
[PlayerManager_430582329311233] Saved board data: player:90000095932 entities:15
[PlayerManager_430582329311233] Saved prev round data: player:90000095932 entities:15
[AutoChessScene_430582329311233] Player 90000011436 has 15 entities to save
[PlayerManager_430582329311233] Saved board data: player:90000011436 entities:15
[PlayerManager_430582329311233] Saved prev round data: player:90000011436 entities:15
[OpponentPairManager] Generating opponent pairs for round 1, active players: 3, eliminated: 0
[OpponentPairManager] Random pair: Player 10102021301 vs Player 90000011436
[OpponentPairManager] Generated 1 opponent pairs for round 1
[AutoChessScene_430582329311233] Created 2 opponent pairs
[PlayerManager_430582329311233] Set player opponents, count: 2
[BattleInstanceManager] Created instance 430582329311233_1 for active players 10102021301 vs 90000011436
[CheckerBoard_430582329311233] Cleared checkerboard
[CheckerBoard_430582329311233] Initialized
[BattleInstance] 430582329311233_1 created with players: 10102021301, 90000011436
[BattleInstanceManager] Created 1 battle instances for 2 players
[AutoChessScene_430582329311233] Restoring player 10102021301 to My area (rows 1-5)
[CheckerBoard_430582329311233] Placed entity 1 at position (1, 6)
[CheckerBoard_430582329311233] Created entity ID:1 ConfigID:101 at (1, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 1: (6,6)->(1,6), GridID:36->6
[CheckerBoard_430582329311233] Placed entity 2 at position (3, 4)
[CheckerBoard_430582329311233] Created entity ID:2 ConfigID:101 at (3, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 2: (8,4)->(3,4), GridID:46->16
[CheckerBoard_430582329311233] Placed entity 3 at position (4, 1)
[CheckerBoard_430582329311233] Created entity ID:3 ConfigID:103 at (4, 1) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 3: (9,1)->(4,1), GridID:49->19
[CheckerBoard_430582329311233] Placed entity 4 at position (3, 3)
[CheckerBoard_430582329311233] Created entity ID:4 ConfigID:102 at (3, 3) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 4: (8,3)->(3,3), GridID:45->15
[CheckerBoard_430582329311233] Placed entity 5 at position (4, 6)
[CheckerBoard_430582329311233] Created entity ID:5 ConfigID:102 at (4, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 5: (9,6)->(4,6), GridID:54->24
[CheckerBoard_430582329311233] Placed entity 6 at position (2, 4)
[CheckerBoard_430582329311233] Created entity ID:6 ConfigID:103 at (2, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 6: (7,4)->(2,4), GridID:40->10
[CheckerBoard_430582329311233] Placed entity 7 at position (2, 3)
[CheckerBoard_430582329311233] Created entity ID:7 ConfigID:101 at (2, 3) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 7: (7,3)->(2,3), GridID:39->9
[CheckerBoard_430582329311233] Placed entity 8 at position (5, 6)
[CheckerBoard_430582329311233] Created entity ID:8 ConfigID:101 at (5, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 8: (10,6)->(5,6), GridID:60->30
[CheckerBoard_430582329311233] Placed entity 9 at position (3, 6)
[CheckerBoard_430582329311233] Created entity ID:9 ConfigID:103 at (3, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 9: (8,6)->(3,6), GridID:48->18
[CheckerBoard_430582329311233] Placed entity 10 at position (1, 1)
[CheckerBoard_430582329311233] Created entity ID:10 ConfigID:103 at (1, 1) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 10: (6,1)->(1,1), GridID:31->1
[CheckerBoard_430582329311233] Placed entity 11 at position (4, 3)
[CheckerBoard_430582329311233] Created entity ID:11 ConfigID:103 at (4, 3) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 11: (9,3)->(4,3), GridID:51->21
[CheckerBoard_430582329311233] Placed entity 12 at position (3, 2)
[CheckerBoard_430582329311233] Created entity ID:12 ConfigID:102 at (3, 2) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 12: (8,2)->(3,2), GridID:44->14
[CheckerBoard_430582329311233] Placed entity 13 at position (5, 5)
[CheckerBoard_430582329311233] Created entity ID:13 ConfigID:101 at (5, 5) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 13: (10,5)->(5,5), GridID:59->29
[CheckerBoard_430582329311233] Placed entity 14 at position (3, 5)
[CheckerBoard_430582329311233] Created entity ID:14 ConfigID:101 at (3, 5) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 14: (8,5)->(3,5), GridID:47->17
[CheckerBoard_430582329311233] Placed entity 15 at position (1, 4)
[CheckerBoard_430582329311233] Created entity ID:15 ConfigID:101 at (1, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 15: (6,4)->(1,4), GridID:34->4
[AutoChessScene_430582329311233] Restored board: player:10102021301 entities:15
[AutoChessScene_430582329311233] No instance found for player 90000095932 when restoring board data
[AutoChessScene_430582329311233] Restoring player 90000011436 to Enemy area (rows 6-10)
[CheckerBoard_430582329311233] Placed entity 16 at position (7, 5)
[CheckerBoard_430582329311233] Created entity ID:16 ConfigID:102 at (7, 5) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 16: (2,5)->(7,5), GridID:11->41
[CheckerBoard_430582329311233] Placed entity 17 at position (10, 4)
[CheckerBoard_430582329311233] Created entity ID:17 ConfigID:103 at (10, 4) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 17: (5,4)->(10,4), GridID:28->58
[CheckerBoard_430582329311233] Placed entity 18 at position (9, 3)
[CheckerBoard_430582329311233] Created entity ID:18 ConfigID:103 at (9, 3) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 18: (4,3)->(9,3), GridID:21->51
[CheckerBoard_430582329311233] Placed entity 19 at position (7, 4)
[CheckerBoard_430582329311233] Created entity ID:19 ConfigID:102 at (7, 4) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 19: (2,4)->(7,4), GridID:10->40
[CheckerBoard_430582329311233] Placed entity 20 at position (10, 6)
[CheckerBoard_430582329311233] Created entity ID:20 ConfigID:103 at (10, 6) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 20: (5,6)->(10,6), GridID:30->60
[CheckerBoard_430582329311233] Placed entity 21 at position (8, 5)
[CheckerBoard_430582329311233] Created entity ID:21 ConfigID:101 at (8, 5) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 21: (3,5)->(8,5), GridID:17->47
[CheckerBoard_430582329311233] Placed entity 22 at position (10, 1)
[CheckerBoard_430582329311233] Created entity ID:22 ConfigID:102 at (10, 1) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 22: (5,1)->(10,1), GridID:25->55
[CheckerBoard_430582329311233] Placed entity 23 at position (6, 2)
[CheckerBoard_430582329311233] Created entity ID:23 ConfigID:101 at (6, 2) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 23: (1,2)->(6,2), GridID:2->32
[CheckerBoard_430582329311233] Placed entity 24 at position (9, 4)
[CheckerBoard_430582329311233] Created entity ID:24 ConfigID:101 at (9, 4) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 24: (4,4)->(9,4), GridID:22->52
[CheckerBoard_430582329311233] Placed entity 25 at position (10, 5)
[CheckerBoard_430582329311233] Created entity ID:25 ConfigID:103 at (10, 5) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 25: (5,5)->(10,5), GridID:29->59
[CheckerBoard_430582329311233] Placed entity 26 at position (6, 5)
[CheckerBoard_430582329311233] Created entity ID:26 ConfigID:103 at (6, 5) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 26: (1,5)->(6,5), GridID:5->35
[CheckerBoard_430582329311233] Placed entity 27 at position (8, 1)
[CheckerBoard_430582329311233] Created entity ID:27 ConfigID:102 at (8, 1) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 27: (3,1)->(8,1), GridID:13->43
[CheckerBoard_430582329311233] Placed entity 28 at position (6, 3)
[CheckerBoard_430582329311233] Created entity ID:28 ConfigID:101 at (6, 3) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 28: (1,3)->(6,3), GridID:3->33
[CheckerBoard_430582329311233] Placed entity 29 at position (8, 2)
[CheckerBoard_430582329311233] Created entity ID:29 ConfigID:103 at (8, 2) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 29: (3,2)->(8,2), GridID:14->44
[CheckerBoard_430582329311233] Placed entity 30 at position (6, 1)
[CheckerBoard_430582329311233] Created entity ID:30 ConfigID:102 at (6, 1) for player 90000011436
[AutoChessScene_430582329311233] Restored entity 30: (1,1)->(6,1), GridID:1->31
[AutoChessScene_430582329311233] Restored board: player:90000011436 entities:15
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 10102021301
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 90000011436
[PlayerManager_430582329311233] Reset all players ready status
[AutoChessScene_430582329311233] Round started with 1 battle instances
[AutoChessScene_430582329311233] Generating buff options for all players
[BuffManager_430582329311233] Generated 3 buff options for player 10102021301: [109, 110, 108]
[AutoChessScene_430582329311233] Generated 3 buff options for player 10102021301: [109, 110, 108]
[BuffManager_430582329311233] Generated 3 buff options for player 90000095932: [108, 102, 107]
[AutoChessScene_430582329311233] Generated 3 buff options for player 90000095932: [108, 102, 107]
[BuffManager_430582329311233] Generated 3 buff options for player 90000011436: [103, 105, 109]
[AutoChessScene_430582329311233] Generated 3 buff options for player 90000011436: [103, 105, 109]
[AutoChessScene_430582329311233] Player status: Total=4, Active=3
[AutoChessScene_430582329311233] Player 10102021301: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430582329311233] Player 90000095932: Eliminated=False, Health=2, HasInstance=False
[AutoChessScene_430582329311233] Player 90000011436: Eliminated=False, Health=1, HasInstance=True
[AutoChessScene_430582329311233] Player 90000022651: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_430582329311233] Sending RoundStart notifications to 3 active players...
[AutoChessScene_430582329311233] RoundStart: Player 10102021301 buff options: [109, 110, 108]
[AutoChessScene_430582329311233] RoundStart board data: player:10102021301 heroes:15
[AutoChessScene_430582329311233] RoundStart board data: player:90000011436 heroes:15
[AutoChessScene_430582329311233] Sending RoundStart to Player 10102021301 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_430582329311233] Player 90000095932 not in battle instance (vs AI), sending RoundStart notification anyway
[AutoChessScene_430582329311233] RoundStart: Player 90000095932 buff options: [108, 102, 107]
[AutoChessScene_430582329311233] RoundStart self board: player:90000095932 heroes:15
[AutoChessScene_430582329311233] Sent RoundStart to Player 90000095932 (vs AI) on GameServer 10102
[AutoChessScene_430582329311233] RoundStart: Player 90000011436 buff options: [103, 105, 109]
[AutoChessScene_430582329311233] RoundStart board data: player:10102021301 heroes:15
[AutoChessScene_430582329311233] RoundStart board data: player:90000011436 heroes:15
[AutoChessScene_430582329311233] Sending RoundStart to Player 90000011436 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_430582329311233] Successfully sent RoundStart notifications to all 3 players via NATS
[BattleService] Updated battle 430582329311233 state to StateRoundStart
[AutoChessScene_430582329311233] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R4)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleStateManager_430582329311233] ===== ROUND 4 INITIALIZATION COMPLETE =====
[BattleStateManager_430582329311233] Buff selection timer started: 25000ms
[BattleStateManager_430582329311233] State: StateRoundStart -> StatePreparation (R4, 65000ms)
[AutoChessScene_430582329311233] Preparation phase started
[BuffManager_430582329311233] Added buff 108 (Buff_108) to player 90000095932
[AutoChessScene_430582329311233] Auto-selected buff 108 for bot player 90000095932
[AutoChessScene_430582329311233] No battle instance found for player 90000095932
[AutoChessScene_430582329311233] Generated 0 new heroes for bot player 90000095932 after buff selection
[BuffManager_430582329311233] Added buff 103 (Buff_103) to player 90000011436
[AutoChessScene_430582329311233] Auto-selected buff 103 for bot player 90000011436
[CheckerBoard_430582329311233] Placed entity 31 at position (9, 5)
[CheckerBoard_430582329311233] Created entity ID:31 ConfigID:101 at (9, 5) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 32 at position (7, 2)
[CheckerBoard_430582329311233] Created entity ID:32 ConfigID:102 at (7, 2) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 33 at position (7, 3)
[CheckerBoard_430582329311233] Created entity ID:33 ConfigID:103 at (7, 3) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 34 at position (9, 1)
[CheckerBoard_430582329311233] Created entity ID:34 ConfigID:101 at (9, 1) for player 90000011436
[CheckerBoard_430582329311233] Placed entity 35 at position (7, 6)
[CheckerBoard_430582329311233] Created entity ID:35 ConfigID:103 at (7, 6) for player 90000011436
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 90000011436: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 90000011436 in Enemy area
[AutoChessScene_430582329311233] Generated 5 heroes for player 90000011436: 5 placed on board, 0 in temporary slots
[AutoChessScene_430582329311233] Generated 5 new heroes for bot player 90000011436 after buff selection
[AutoChessScene_430582329311233] No buff options available for bot player 90000022651
[PlayerManager_430582329311233] Player 90000095932 ready status set to True
[PlayerManager_430582329311233] Player 90000011436 ready status set to True
[AutoChessScene_430582329311233] Auto-ready 2 additional bots
[AutoChessScene_430582329311233] Free operation phase started
[BattleService] Updated battle 430582329311233 state to StatePreparation
[AutoChessScene_430582329311233] State change sent to GameServer: StateRoundStart -> StatePreparation (R4)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED SelectBuffer RPC =====
[BattleService] Player 10102021301 is selecting buff 108
[BuffManager_430582329311233] Added buff 108 (Buff_108) to player 10102021301
[CheckerBoard_430582329311233] Placed entity 36 at position (1, 3)
[CheckerBoard_430582329311233] Created entity ID:36 ConfigID:101 at (1, 3) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 37 at position (4, 5)
[CheckerBoard_430582329311233] Created entity ID:37 ConfigID:101 at (4, 5) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 38 at position (4, 2)
[CheckerBoard_430582329311233] Created entity ID:38 ConfigID:102 at (4, 2) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 39 at position (2, 2)
[CheckerBoard_430582329311233] Created entity ID:39 ConfigID:102 at (2, 2) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 40 at position (4, 4)
[CheckerBoard_430582329311233] Created entity ID:40 ConfigID:103 at (4, 4) for player 10102021301
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 10102021301: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 10102021301 in My area
[AutoChessScene_430582329311233] Generated 5 heroes for player 10102021301: 5 placed on board, 0 in temporary slots
[AutoChessScene_430582329311233] Player 10102021301 selected buff 108, generated 5 new heroes
[BattleService] SelectBuffer response: Code=0, NewHeroes=5
[PlayerManager_430582329311233] Player 10102021301 ready status set to True
[PlayerManager_430582329311233] All players are ready!
[AutoChessScene_430582329311233] All players are ready, transitioning to next state
[BattleStateManager_430582329311233] State: StatePreparation -> StateBattleStarting (R4, 1000ms)
[AutoChessScene_430582329311233] Applying battle start buffs for all players
[BuffManager_430582329311233] Applying battle start buff 105 (Buff_105) for player 10102021301
[BuffManager_430582329311233] Applying battle start buff 108 (Buff_108) for player 10102021301
[BuffManager_430582329311233] Applying battle start buff 106 (Buff_106) for player 90000095932
[BuffManager_430582329311233] Applying battle start buff 108 (Buff_108) for player 90000095932
[BuffManager_430582329311233] Applying battle start buff 102 (Buff_102) for player 90000011436
[BuffManager_430582329311233] Applying battle start buff 103 (Buff_103) for player 90000011436
[AutoChessScene_430582329311233] Camp info for player 10102021301: 20 heroes added
[AutoChessScene_430582329311233] Camp info for player 90000011436: 20 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 10102021301, Team order: [10102021301, 90000011436], total GridIDs used: 40
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 10102021301 vs Opponent 90000011436 with 2 teams
[AutoChessScene_430582329311233] Player 90000095932 not in any battle instance, skipping RoundBattleStart notification
[AutoChessScene_430582329311233] Camp info for player 10102021301: 20 heroes added
[AutoChessScene_430582329311233] Camp info for player 90000011436: 20 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 90000011436, Team order: [10102021301, 90000011436], total GridIDs used: 40
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 90000011436 vs Opponent 10102021301 with 2 teams
[AutoChessScene_430582329311233] Sent RoundBattleStart notifications with seed: 862837516
[BattleService] Updated battle 430582329311233 state to StateBattleStarting
[AutoChessScene_430582329311233] State change sent to GameServer: StatePreparation -> StateBattleStarting (R4)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[AutoChessScene_430582329311233] Player 10102021301 set ready status to True
[BattleStateManager_430582329311233] State: StateBattleStarting -> StateBattleInProgress (R4, 65000ms)
[AutoChessScene_430582329311233] Starting all battle instances
[BattleInstance] 430582329311233_1 battle started
[BattleInstanceManager] Started all 1 battle instances
[AutoChessScene_430582329311233] Bot 90000011436 vs real player 10102021301, waiting for real player result
[AutoChessScene_430582329311233] Bot vs real player battles will be handled by system timeout (62s)
[BattleService] Updated battle 430582329311233 state to StateBattleInProgress
[AutoChessScene_430582329311233] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R4)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] EndBattle uid: 10102021301, win: True
[AutoChessScene_430582329311233] Player 10102021301 sent EndBattleReq (win: True), instance: 430582329311233_1
[AutoChessScene_430582329311233] Auto EndBattle for bot 90000011436 vs real player 10102021301, bot result: False
[BattleInstance] 430582329311233_1 battle finished, winner: 10102021301, loser: 90000011436
[AutoChessScene_430582329311233] Battle instance 430582329311233_1 completed: Winner 10102021301, Loser 90000011436
[AutoChessScene_430582329311233] All battle instances finished, proceeding to settlement
[BattleStateManager_430582329311233] State: StateBattleInProgress -> StateRoundSettlement (R4, 5000ms)
[AutoChessScene_430582329311233] Processing battle results
[PlayerManager_430582329311233] Player 90000011436 health reduced by 1, current health: 0
[PlayerManager_430582329311233] Player 90000011436 eliminated from battle 430582329311233
[AutoChessScene_430582329311233] Player 90000011436 has been eliminated
[AutoChessScene_430582329311233] Cleared 0 entities for player 90000011436
[AutoChessScene_430582329311233] Player 90000011436 lost 1 health, winner: 10102021301
[AutoChessScene_430582329311233] Sent RoundBattleEnd notification (isEnd=false) - Winner: 10102021301, Loser: 90000011436
[BattleStateManager_430582329311233] Invalid state transition from StateRoundSettlement to StateRoundSettlement
[AutoChessScene_430582329311233] Checking players elimination
[AutoChessScene_430582329311233] Active players remaining: 2
[AutoChessScene_430582329311233] Waiting for all players to confirm round settlement before starting new round
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000095932
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000011436
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000022651
[BattleService] Updated battle 430582329311233 state to StateRoundSettlement
[AutoChessScene_430582329311233] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R4)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10102021301 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10102021301 confirmed round settlement, current state: StateRoundSettlement
[AutoChessScene_430582329311233] Player 10102021301 confirmed round settlement, count: 4
[AutoChessScene_430582329311233] Real player 10102021301 confirmed, auto-confirming all bots
[AutoChessScene_430582329311233] All players confirmed round settlement, starting new round
[PlayerManager_430582329311233] Player 10102021301 ready status set to False
[PlayerManager_430582329311233] Player 90000095932 ready status set to False
[BattleStateManager_430582329311233] ===== STARTING NEW ROUND 5 =====
[BattleStateManager_430582329311233] Round 5 has buff selection: False
[BattleStateManager_430582329311233] Publishing RoundStartedEvent for round 5
[AutoChessScene_430582329311233] Round 5 started
[BattleStateManager_430582329311233] Setting state to StateRoundStart for round 5
[BattleStateManager_430582329311233] State: StateRoundSettlement -> StateRoundStart (R5, 1000ms)
[AutoChessScene_430582329311233] HandleRoundStart: 2 active players
[AutoChessScene_430582329311233] Valid player: 10102021301, Health: 3
[AutoChessScene_430582329311233] Valid player: 90000095932, Health: 2
[AutoChessScene_430582329311233] Player 10102021301 has 20 entities to save
[PlayerManager_430582329311233] Saved board data: player:10102021301 entities:20
[PlayerManager_430582329311233] Saved prev round data: player:10102021301 entities:20
[AutoChessScene_430582329311233] No instance found for player 90000095932 when saving board data
[OpponentPairManager] Generating opponent pairs for round 1, active players: 2, eliminated: 0
[OpponentPairManager] Random pair: Player 10102021301 vs Player 90000095932
[OpponentPairManager] Generated 1 opponent pairs for round 1
[AutoChessScene_430582329311233] Created 2 opponent pairs
[PlayerManager_430582329311233] Set player opponents, count: 2
[BattleInstanceManager] Created instance 430582329311233_1 for active players 10102021301 vs 90000095932
[CheckerBoard_430582329311233] Cleared checkerboard
[CheckerBoard_430582329311233] Initialized
[BattleInstance] 430582329311233_1 created with players: 10102021301, 90000095932
[BattleInstanceManager] Created 1 battle instances for 2 players
[AutoChessScene_430582329311233] Restoring player 10102021301 to My area (rows 1-5)
[CheckerBoard_430582329311233] Placed entity 1 at position (1, 6)
[CheckerBoard_430582329311233] Created entity ID:1 ConfigID:101 at (1, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 1: (1,6)->(1,6), GridID:6->6
[CheckerBoard_430582329311233] Placed entity 2 at position (3, 4)
[CheckerBoard_430582329311233] Created entity ID:2 ConfigID:101 at (3, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 2: (3,4)->(3,4), GridID:16->16
[CheckerBoard_430582329311233] Placed entity 3 at position (4, 1)
[CheckerBoard_430582329311233] Created entity ID:3 ConfigID:103 at (4, 1) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 3: (4,1)->(4,1), GridID:19->19
[CheckerBoard_430582329311233] Placed entity 4 at position (3, 3)
[CheckerBoard_430582329311233] Created entity ID:4 ConfigID:102 at (3, 3) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 4: (3,3)->(3,3), GridID:15->15
[CheckerBoard_430582329311233] Placed entity 5 at position (4, 6)
[CheckerBoard_430582329311233] Created entity ID:5 ConfigID:102 at (4, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 5: (4,6)->(4,6), GridID:24->24
[CheckerBoard_430582329311233] Placed entity 6 at position (2, 4)
[CheckerBoard_430582329311233] Created entity ID:6 ConfigID:103 at (2, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 6: (2,4)->(2,4), GridID:10->10
[CheckerBoard_430582329311233] Placed entity 7 at position (2, 3)
[CheckerBoard_430582329311233] Created entity ID:7 ConfigID:101 at (2, 3) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 7: (2,3)->(2,3), GridID:9->9
[CheckerBoard_430582329311233] Placed entity 8 at position (5, 6)
[CheckerBoard_430582329311233] Created entity ID:8 ConfigID:101 at (5, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 8: (5,6)->(5,6), GridID:30->30
[CheckerBoard_430582329311233] Placed entity 9 at position (3, 6)
[CheckerBoard_430582329311233] Created entity ID:9 ConfigID:103 at (3, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 9: (3,6)->(3,6), GridID:18->18
[CheckerBoard_430582329311233] Placed entity 10 at position (1, 1)
[CheckerBoard_430582329311233] Created entity ID:10 ConfigID:103 at (1, 1) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 10: (1,1)->(1,1), GridID:1->1
[CheckerBoard_430582329311233] Placed entity 11 at position (4, 3)
[CheckerBoard_430582329311233] Created entity ID:11 ConfigID:103 at (4, 3) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 11: (4,3)->(4,3), GridID:21->21
[CheckerBoard_430582329311233] Placed entity 12 at position (3, 2)
[CheckerBoard_430582329311233] Created entity ID:12 ConfigID:102 at (3, 2) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 12: (3,2)->(3,2), GridID:14->14
[CheckerBoard_430582329311233] Placed entity 13 at position (5, 5)
[CheckerBoard_430582329311233] Created entity ID:13 ConfigID:101 at (5, 5) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 13: (5,5)->(5,5), GridID:29->29
[CheckerBoard_430582329311233] Placed entity 14 at position (3, 5)
[CheckerBoard_430582329311233] Created entity ID:14 ConfigID:101 at (3, 5) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 14: (3,5)->(3,5), GridID:17->17
[CheckerBoard_430582329311233] Placed entity 15 at position (1, 4)
[CheckerBoard_430582329311233] Created entity ID:15 ConfigID:101 at (1, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 15: (1,4)->(1,4), GridID:4->4
[CheckerBoard_430582329311233] Placed entity 16 at position (1, 3)
[CheckerBoard_430582329311233] Created entity ID:16 ConfigID:101 at (1, 3) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 16: (1,3)->(1,3), GridID:3->3
[CheckerBoard_430582329311233] Placed entity 17 at position (4, 5)
[CheckerBoard_430582329311233] Created entity ID:17 ConfigID:101 at (4, 5) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 17: (4,5)->(4,5), GridID:23->23
[CheckerBoard_430582329311233] Placed entity 18 at position (4, 2)
[CheckerBoard_430582329311233] Created entity ID:18 ConfigID:102 at (4, 2) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 18: (4,2)->(4,2), GridID:20->20
[CheckerBoard_430582329311233] Placed entity 19 at position (2, 2)
[CheckerBoard_430582329311233] Created entity ID:19 ConfigID:102 at (2, 2) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 19: (2,2)->(2,2), GridID:8->8
[CheckerBoard_430582329311233] Placed entity 20 at position (4, 4)
[CheckerBoard_430582329311233] Created entity ID:20 ConfigID:103 at (4, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 20: (4,4)->(4,4), GridID:22->22
[AutoChessScene_430582329311233] Restored board: player:10102021301 entities:20
[AutoChessScene_430582329311233] Restoring player 90000095932 to Enemy area (rows 6-10)
[CheckerBoard_430582329311233] Placed entity 21 at position (9, 5)
[CheckerBoard_430582329311233] Created entity ID:21 ConfigID:101 at (9, 5) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 21: (9,5)->(9,5), GridID:53->53
[CheckerBoard_430582329311233] Placed entity 22 at position (9, 3)
[CheckerBoard_430582329311233] Created entity ID:22 ConfigID:103 at (9, 3) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 22: (9,3)->(9,3), GridID:51->51
[CheckerBoard_430582329311233] Placed entity 23 at position (9, 6)
[CheckerBoard_430582329311233] Created entity ID:23 ConfigID:101 at (9, 6) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 23: (9,6)->(9,6), GridID:54->54
[CheckerBoard_430582329311233] Placed entity 24 at position (6, 2)
[CheckerBoard_430582329311233] Created entity ID:24 ConfigID:101 at (6, 2) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 24: (6,2)->(6,2), GridID:32->32
[CheckerBoard_430582329311233] Placed entity 25 at position (7, 4)
[CheckerBoard_430582329311233] Created entity ID:25 ConfigID:101 at (7, 4) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 25: (7,4)->(7,4), GridID:40->40
[CheckerBoard_430582329311233] Placed entity 26 at position (8, 3)
[CheckerBoard_430582329311233] Created entity ID:26 ConfigID:101 at (8, 3) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 26: (8,3)->(8,3), GridID:45->45
[CheckerBoard_430582329311233] Placed entity 27 at position (6, 6)
[CheckerBoard_430582329311233] Created entity ID:27 ConfigID:101 at (6, 6) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 27: (6,6)->(6,6), GridID:36->36
[CheckerBoard_430582329311233] Placed entity 28 at position (6, 5)
[CheckerBoard_430582329311233] Created entity ID:28 ConfigID:103 at (6, 5) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 28: (6,5)->(6,5), GridID:35->35
[CheckerBoard_430582329311233] Placed entity 29 at position (10, 1)
[CheckerBoard_430582329311233] Created entity ID:29 ConfigID:101 at (10, 1) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 29: (10,1)->(10,1), GridID:55->55
[CheckerBoard_430582329311233] Placed entity 30 at position (7, 3)
[CheckerBoard_430582329311233] Created entity ID:30 ConfigID:101 at (7, 3) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 30: (7,3)->(7,3), GridID:39->39
[CheckerBoard_430582329311233] Placed entity 31 at position (9, 2)
[CheckerBoard_430582329311233] Created entity ID:31 ConfigID:102 at (9, 2) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 31: (9,2)->(9,2), GridID:50->50
[CheckerBoard_430582329311233] Placed entity 32 at position (8, 6)
[CheckerBoard_430582329311233] Created entity ID:32 ConfigID:101 at (8, 6) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 32: (8,6)->(8,6), GridID:48->48
[CheckerBoard_430582329311233] Placed entity 33 at position (10, 2)
[CheckerBoard_430582329311233] Created entity ID:33 ConfigID:103 at (10, 2) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 33: (10,2)->(10,2), GridID:56->56
[CheckerBoard_430582329311233] Placed entity 34 at position (9, 1)
[CheckerBoard_430582329311233] Created entity ID:34 ConfigID:102 at (9, 1) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 34: (9,1)->(9,1), GridID:49->49
[CheckerBoard_430582329311233] Placed entity 35 at position (8, 2)
[CheckerBoard_430582329311233] Created entity ID:35 ConfigID:101 at (8, 2) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 35: (8,2)->(8,2), GridID:44->44
[AutoChessScene_430582329311233] Restored board: player:90000095932 entities:15
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 10102021301
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 90000095932
[PlayerManager_430582329311233] Reset all players ready status
[AutoChessScene_430582329311233] Round started with 1 battle instances
[AutoChessScene_430582329311233] Generating 5 heroes for all players in round 5
[CheckerBoard_430582329311233] Placed entity 36 at position (2, 1)
[CheckerBoard_430582329311233] Created entity ID:36 ConfigID:102 at (2, 1) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 37 at position (5, 4)
[CheckerBoard_430582329311233] Created entity ID:37 ConfigID:103 at (5, 4) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 38 at position (5, 1)
[CheckerBoard_430582329311233] Created entity ID:38 ConfigID:102 at (5, 1) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 39 at position (1, 5)
[CheckerBoard_430582329311233] Created entity ID:39 ConfigID:103 at (1, 5) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 40 at position (5, 2)
[CheckerBoard_430582329311233] Created entity ID:40 ConfigID:101 at (5, 2) for player 10102021301
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 10102021301: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 10102021301 in My area
[AutoChessScene_430582329311233] Generated 5 heroes for player 10102021301: 5 placed on board, 0 in temporary slots
[CheckerBoard_430582329311233] Placed entity 41 at position (10, 4)
[CheckerBoard_430582329311233] Created entity ID:41 ConfigID:101 at (10, 4) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 42 at position (9, 4)
[CheckerBoard_430582329311233] Created entity ID:42 ConfigID:101 at (9, 4) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 43 at position (7, 1)
[CheckerBoard_430582329311233] Created entity ID:43 ConfigID:101 at (7, 1) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 44 at position (8, 5)
[CheckerBoard_430582329311233] Created entity ID:44 ConfigID:103 at (8, 5) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 45 at position (8, 1)
[CheckerBoard_430582329311233] Created entity ID:45 ConfigID:101 at (8, 1) for player 90000095932
[CheckerBoard_430582329311233] CheckTimes limit (4) reached for 1 hero types for player 90000095932
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 90000095932: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 90000095932 in Enemy area
[AutoChessScene_430582329311233] Generated 5 heroes for player 90000095932: 5 placed on board, 0 in temporary slots
[AutoChessScene_430582329311233] Player status: Total=4, Active=2
[AutoChessScene_430582329311233] Player 10102021301: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430582329311233] Player 90000095932: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_430582329311233] Player 90000011436: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_430582329311233] Player 90000022651: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_430582329311233] Sending RoundStart notifications to 2 active players...
[AutoChessScene_430582329311233] RoundStart board data: player:10102021301 heroes:25
[AutoChessScene_430582329311233] RoundStart board data: player:90000095932 heroes:20
[AutoChessScene_430582329311233] Sending RoundStart to Player 10102021301 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430582329311233] RoundStart board data: player:10102021301 heroes:25
[AutoChessScene_430582329311233] RoundStart board data: player:90000095932 heroes:20
[AutoChessScene_430582329311233] Sending RoundStart to Player 90000095932 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_430582329311233] Successfully sent RoundStart notifications to all 2 players via NATS
[BattleService] Updated battle 430582329311233 state to StateRoundStart
[AutoChessScene_430582329311233] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R5)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleStateManager_430582329311233] ===== ROUND 5 INITIALIZATION COMPLETE =====
[BattleStateManager_430582329311233] State: StateRoundStart -> StatePreparation (R5, 65000ms)
[AutoChessScene_430582329311233] Preparation phase started
[PlayerManager_430582329311233] Player 90000095932 ready status set to True
[AutoChessScene_430582329311233] Auto-ready 1 additional bots
[AutoChessScene_430582329311233] Free operation phase started
[BattleService] Updated battle 430582329311233 state to StatePreparation
[AutoChessScene_430582329311233] State change sent to GameServer: StateRoundStart -> StatePreparation (R5)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[PlayerManager_430582329311233] Player 10102021301 ready status set to True
[PlayerManager_430582329311233] All players are ready!
[AutoChessScene_430582329311233] All players are ready, transitioning to next state
[BattleStateManager_430582329311233] State: StatePreparation -> StateBattleStarting (R5, 1000ms)
[AutoChessScene_430582329311233] Applying battle start buffs for all players
[BuffManager_430582329311233] Applying battle start buff 105 (Buff_105) for player 10102021301
[BuffManager_430582329311233] Applying battle start buff 108 (Buff_108) for player 10102021301
[BuffManager_430582329311233] Applying battle start buff 106 (Buff_106) for player 90000095932
[BuffManager_430582329311233] Applying battle start buff 108 (Buff_108) for player 90000095932
[AutoChessScene_430582329311233] Camp info for player 10102021301: 25 heroes added
[AutoChessScene_430582329311233] Camp info for player 90000095932: 20 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 10102021301, Team order: [10102021301, 90000095932], total GridIDs used: 45
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 10102021301 vs Opponent 90000095932 with 2 teams
[AutoChessScene_430582329311233] Camp info for player 10102021301: 25 heroes added
[AutoChessScene_430582329311233] Camp info for player 90000095932: 20 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 90000095932, Team order: [10102021301, 90000095932], total GridIDs used: 45
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 90000095932 vs Opponent 10102021301 with 2 teams
[AutoChessScene_430582329311233] Sent RoundBattleStart notifications with seed: 127369325
[BattleService] Updated battle 430582329311233 state to StateBattleStarting
[AutoChessScene_430582329311233] State change sent to GameServer: StatePreparation -> StateBattleStarting (R5)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[AutoChessScene_430582329311233] Player 10102021301 set ready status to True
[BattleStateManager_430582329311233] State: StateBattleStarting -> StateBattleInProgress (R5, 65000ms)
[AutoChessScene_430582329311233] Starting all battle instances
[BattleInstance] 430582329311233_1 battle started
[BattleInstanceManager] Started all 1 battle instances
[AutoChessScene_430582329311233] Bot 90000095932 vs real player 10102021301, waiting for real player result
[AutoChessScene_430582329311233] Bot vs real player battles will be handled by system timeout (62s)
[BattleService] Updated battle 430582329311233 state to StateBattleInProgress
[AutoChessScene_430582329311233] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R5)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] EndBattle uid: 10102021301, win: True
[AutoChessScene_430582329311233] Player 10102021301 sent EndBattleReq (win: True), instance: 430582329311233_1
[AutoChessScene_430582329311233] Auto EndBattle for bot 90000095932 vs real player 10102021301, bot result: False
[BattleInstance] 430582329311233_1 battle finished, winner: 10102021301, loser: 90000095932
[AutoChessScene_430582329311233] Battle instance 430582329311233_1 completed: Winner 10102021301, Loser 90000095932
[AutoChessScene_430582329311233] All battle instances finished, proceeding to settlement
[BattleStateManager_430582329311233] State: StateBattleInProgress -> StateRoundSettlement (R5, 5000ms)
[AutoChessScene_430582329311233] Processing battle results
[PlayerManager_430582329311233] Player 90000095932 health reduced by 1, current health: 1
[AutoChessScene_430582329311233] Player 90000095932 lost 1 health, winner: 10102021301
[AutoChessScene_430582329311233] Sent RoundBattleEnd notification (isEnd=false) - Winner: 10102021301, Loser: 90000095932
[BattleStateManager_430582329311233] Invalid state transition from StateRoundSettlement to StateRoundSettlement
[AutoChessScene_430582329311233] Checking players elimination
[AutoChessScene_430582329311233] Active players remaining: 2
[AutoChessScene_430582329311233] Waiting for all players to confirm round settlement before starting new round
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000095932
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000011436
[AutoChessScene_430582329311233] Auto-confirming round settlement for bot 90000022651
[BattleService] Updated battle 430582329311233 state to StateRoundSettlement
[AutoChessScene_430582329311233] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R5)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10102021301 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10102021301 confirmed round settlement, current state: StateRoundSettlement
[AutoChessScene_430582329311233] Player 10102021301 confirmed round settlement, count: 4
[AutoChessScene_430582329311233] Real player 10102021301 confirmed, auto-confirming all bots
[AutoChessScene_430582329311233] All players confirmed round settlement, starting new round
[PlayerManager_430582329311233] Player 10102021301 ready status set to False
[PlayerManager_430582329311233] Player 90000095932 ready status set to False
[BattleStateManager_430582329311233] ===== STARTING NEW ROUND 6 =====
[BattleStateManager_430582329311233] Round 6 has buff selection: True
[BattleStateManager_430582329311233] Publishing RoundStartedEvent for round 6
[AutoChessScene_430582329311233] Round 6 started
[BattleStateManager_430582329311233] Setting state to StateRoundStart for round 6
[BattleStateManager_430582329311233] State: StateRoundSettlement -> StateRoundStart (R6, 1000ms)
[AutoChessScene_430582329311233] HandleRoundStart: 2 active players
[AutoChessScene_430582329311233] Valid player: 10102021301, Health: 3
[AutoChessScene_430582329311233] Valid player: 90000095932, Health: 1
[AutoChessScene_430582329311233] Player 10102021301 has 25 entities to save
[PlayerManager_430582329311233] Saved board data: player:10102021301 entities:25
[PlayerManager_430582329311233] Saved prev round data: player:10102021301 entities:25
[AutoChessScene_430582329311233] Player 90000095932 has 20 entities to save
[PlayerManager_430582329311233] Saved board data: player:90000095932 entities:20
[PlayerManager_430582329311233] Saved prev round data: player:90000095932 entities:20
[OpponentPairManager] Generating opponent pairs for round 1, active players: 2, eliminated: 0
[OpponentPairManager] Random pair: Player 10102021301 vs Player 90000095932
[OpponentPairManager] Generated 1 opponent pairs for round 1
[AutoChessScene_430582329311233] Created 2 opponent pairs
[PlayerManager_430582329311233] Set player opponents, count: 2
[BattleInstanceManager] Created instance 430582329311233_1 for active players 10102021301 vs 90000095932
[CheckerBoard_430582329311233] Cleared checkerboard
[CheckerBoard_430582329311233] Initialized
[BattleInstance] 430582329311233_1 created with players: 10102021301, 90000095932
[BattleInstanceManager] Created 1 battle instances for 2 players
[AutoChessScene_430582329311233] Restoring player 10102021301 to My area (rows 1-5)
[CheckerBoard_430582329311233] Placed entity 1 at position (1, 6)
[CheckerBoard_430582329311233] Created entity ID:1 ConfigID:101 at (1, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 1: (1,6)->(1,6), GridID:6->6
[CheckerBoard_430582329311233] Placed entity 2 at position (3, 4)
[CheckerBoard_430582329311233] Created entity ID:2 ConfigID:101 at (3, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 2: (3,4)->(3,4), GridID:16->16
[CheckerBoard_430582329311233] Placed entity 3 at position (4, 1)
[CheckerBoard_430582329311233] Created entity ID:3 ConfigID:103 at (4, 1) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 3: (4,1)->(4,1), GridID:19->19
[CheckerBoard_430582329311233] Placed entity 4 at position (3, 3)
[CheckerBoard_430582329311233] Created entity ID:4 ConfigID:102 at (3, 3) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 4: (3,3)->(3,3), GridID:15->15
[CheckerBoard_430582329311233] Placed entity 5 at position (4, 6)
[CheckerBoard_430582329311233] Created entity ID:5 ConfigID:102 at (4, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 5: (4,6)->(4,6), GridID:24->24
[CheckerBoard_430582329311233] Placed entity 6 at position (2, 4)
[CheckerBoard_430582329311233] Created entity ID:6 ConfigID:103 at (2, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 6: (2,4)->(2,4), GridID:10->10
[CheckerBoard_430582329311233] Placed entity 7 at position (2, 3)
[CheckerBoard_430582329311233] Created entity ID:7 ConfigID:101 at (2, 3) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 7: (2,3)->(2,3), GridID:9->9
[CheckerBoard_430582329311233] Placed entity 8 at position (5, 6)
[CheckerBoard_430582329311233] Created entity ID:8 ConfigID:101 at (5, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 8: (5,6)->(5,6), GridID:30->30
[CheckerBoard_430582329311233] Placed entity 9 at position (3, 6)
[CheckerBoard_430582329311233] Created entity ID:9 ConfigID:103 at (3, 6) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 9: (3,6)->(3,6), GridID:18->18
[CheckerBoard_430582329311233] Placed entity 10 at position (1, 1)
[CheckerBoard_430582329311233] Created entity ID:10 ConfigID:103 at (1, 1) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 10: (1,1)->(1,1), GridID:1->1
[CheckerBoard_430582329311233] Placed entity 11 at position (4, 3)
[CheckerBoard_430582329311233] Created entity ID:11 ConfigID:103 at (4, 3) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 11: (4,3)->(4,3), GridID:21->21
[CheckerBoard_430582329311233] Placed entity 12 at position (3, 2)
[CheckerBoard_430582329311233] Created entity ID:12 ConfigID:102 at (3, 2) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 12: (3,2)->(3,2), GridID:14->14
[CheckerBoard_430582329311233] Placed entity 13 at position (5, 5)
[CheckerBoard_430582329311233] Created entity ID:13 ConfigID:101 at (5, 5) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 13: (5,5)->(5,5), GridID:29->29
[CheckerBoard_430582329311233] Placed entity 14 at position (3, 5)
[CheckerBoard_430582329311233] Created entity ID:14 ConfigID:101 at (3, 5) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 14: (3,5)->(3,5), GridID:17->17
[CheckerBoard_430582329311233] Placed entity 15 at position (1, 4)
[CheckerBoard_430582329311233] Created entity ID:15 ConfigID:101 at (1, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 15: (1,4)->(1,4), GridID:4->4
[CheckerBoard_430582329311233] Placed entity 16 at position (1, 3)
[CheckerBoard_430582329311233] Created entity ID:16 ConfigID:101 at (1, 3) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 16: (1,3)->(1,3), GridID:3->3
[CheckerBoard_430582329311233] Placed entity 17 at position (4, 5)
[CheckerBoard_430582329311233] Created entity ID:17 ConfigID:101 at (4, 5) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 17: (4,5)->(4,5), GridID:23->23
[CheckerBoard_430582329311233] Placed entity 18 at position (4, 2)
[CheckerBoard_430582329311233] Created entity ID:18 ConfigID:102 at (4, 2) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 18: (4,2)->(4,2), GridID:20->20
[CheckerBoard_430582329311233] Placed entity 19 at position (2, 2)
[CheckerBoard_430582329311233] Created entity ID:19 ConfigID:102 at (2, 2) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 19: (2,2)->(2,2), GridID:8->8
[CheckerBoard_430582329311233] Placed entity 20 at position (4, 4)
[CheckerBoard_430582329311233] Created entity ID:20 ConfigID:103 at (4, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 20: (4,4)->(4,4), GridID:22->22
[CheckerBoard_430582329311233] Placed entity 21 at position (2, 1)
[CheckerBoard_430582329311233] Created entity ID:21 ConfigID:102 at (2, 1) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 21: (2,1)->(2,1), GridID:7->7
[CheckerBoard_430582329311233] Placed entity 22 at position (5, 4)
[CheckerBoard_430582329311233] Created entity ID:22 ConfigID:103 at (5, 4) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 22: (5,4)->(5,4), GridID:28->28
[CheckerBoard_430582329311233] Placed entity 23 at position (5, 1)
[CheckerBoard_430582329311233] Created entity ID:23 ConfigID:102 at (5, 1) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 23: (5,1)->(5,1), GridID:25->25
[CheckerBoard_430582329311233] Placed entity 24 at position (1, 5)
[CheckerBoard_430582329311233] Created entity ID:24 ConfigID:103 at (1, 5) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 24: (1,5)->(1,5), GridID:5->5
[CheckerBoard_430582329311233] Placed entity 25 at position (5, 2)
[CheckerBoard_430582329311233] Created entity ID:25 ConfigID:101 at (5, 2) for player 10102021301
[AutoChessScene_430582329311233] Restored entity 25: (5,2)->(5,2), GridID:26->26
[AutoChessScene_430582329311233] Restored board: player:10102021301 entities:25
[AutoChessScene_430582329311233] Restoring player 90000095932 to Enemy area (rows 6-10)
[CheckerBoard_430582329311233] Placed entity 26 at position (9, 5)
[CheckerBoard_430582329311233] Created entity ID:26 ConfigID:101 at (9, 5) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 26: (9,5)->(9,5), GridID:53->53
[CheckerBoard_430582329311233] Placed entity 27 at position (9, 3)
[CheckerBoard_430582329311233] Created entity ID:27 ConfigID:103 at (9, 3) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 27: (9,3)->(9,3), GridID:51->51
[CheckerBoard_430582329311233] Placed entity 28 at position (9, 6)
[CheckerBoard_430582329311233] Created entity ID:28 ConfigID:101 at (9, 6) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 28: (9,6)->(9,6), GridID:54->54
[CheckerBoard_430582329311233] Placed entity 29 at position (6, 2)
[CheckerBoard_430582329311233] Created entity ID:29 ConfigID:101 at (6, 2) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 29: (6,2)->(6,2), GridID:32->32
[CheckerBoard_430582329311233] Placed entity 30 at position (7, 4)
[CheckerBoard_430582329311233] Created entity ID:30 ConfigID:101 at (7, 4) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 30: (7,4)->(7,4), GridID:40->40
[CheckerBoard_430582329311233] Placed entity 31 at position (8, 3)
[CheckerBoard_430582329311233] Created entity ID:31 ConfigID:101 at (8, 3) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 31: (8,3)->(8,3), GridID:45->45
[CheckerBoard_430582329311233] Placed entity 32 at position (6, 6)
[CheckerBoard_430582329311233] Created entity ID:32 ConfigID:101 at (6, 6) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 32: (6,6)->(6,6), GridID:36->36
[CheckerBoard_430582329311233] Placed entity 33 at position (6, 5)
[CheckerBoard_430582329311233] Created entity ID:33 ConfigID:103 at (6, 5) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 33: (6,5)->(6,5), GridID:35->35
[CheckerBoard_430582329311233] Placed entity 34 at position (10, 1)
[CheckerBoard_430582329311233] Created entity ID:34 ConfigID:101 at (10, 1) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 34: (10,1)->(10,1), GridID:55->55
[CheckerBoard_430582329311233] Placed entity 35 at position (7, 3)
[CheckerBoard_430582329311233] Created entity ID:35 ConfigID:101 at (7, 3) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 35: (7,3)->(7,3), GridID:39->39
[CheckerBoard_430582329311233] Placed entity 36 at position (9, 2)
[CheckerBoard_430582329311233] Created entity ID:36 ConfigID:102 at (9, 2) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 36: (9,2)->(9,2), GridID:50->50
[CheckerBoard_430582329311233] Placed entity 37 at position (8, 6)
[CheckerBoard_430582329311233] Created entity ID:37 ConfigID:101 at (8, 6) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 37: (8,6)->(8,6), GridID:48->48
[CheckerBoard_430582329311233] Placed entity 38 at position (10, 2)
[CheckerBoard_430582329311233] Created entity ID:38 ConfigID:103 at (10, 2) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 38: (10,2)->(10,2), GridID:56->56
[CheckerBoard_430582329311233] Placed entity 39 at position (9, 1)
[CheckerBoard_430582329311233] Created entity ID:39 ConfigID:102 at (9, 1) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 39: (9,1)->(9,1), GridID:49->49
[CheckerBoard_430582329311233] Placed entity 40 at position (8, 2)
[CheckerBoard_430582329311233] Created entity ID:40 ConfigID:101 at (8, 2) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 40: (8,2)->(8,2), GridID:44->44
[CheckerBoard_430582329311233] Placed entity 41 at position (10, 4)
[CheckerBoard_430582329311233] Created entity ID:41 ConfigID:101 at (10, 4) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 41: (10,4)->(10,4), GridID:58->58
[CheckerBoard_430582329311233] Placed entity 42 at position (9, 4)
[CheckerBoard_430582329311233] Created entity ID:42 ConfigID:101 at (9, 4) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 42: (9,4)->(9,4), GridID:52->52
[CheckerBoard_430582329311233] Placed entity 43 at position (7, 1)
[CheckerBoard_430582329311233] Created entity ID:43 ConfigID:101 at (7, 1) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 43: (7,1)->(7,1), GridID:37->37
[CheckerBoard_430582329311233] Placed entity 44 at position (8, 5)
[CheckerBoard_430582329311233] Created entity ID:44 ConfigID:103 at (8, 5) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 44: (8,5)->(8,5), GridID:47->47
[CheckerBoard_430582329311233] Placed entity 45 at position (8, 1)
[CheckerBoard_430582329311233] Created entity ID:45 ConfigID:101 at (8, 1) for player 90000095932
[AutoChessScene_430582329311233] Restored entity 45: (8,1)->(8,1), GridID:43->43
[AutoChessScene_430582329311233] Restored board: player:90000095932 entities:20
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 10102021301
[AutoChessScene_430582329311233] Cleaned orphaned entities for player 90000095932
[PlayerManager_430582329311233] Reset all players ready status
[AutoChessScene_430582329311233] Round started with 1 battle instances
[AutoChessScene_430582329311233] Generating buff options for all players
[BuffManager_430582329311233] Generated 3 buff options for player 10102021301: [107, 109, 101]
[AutoChessScene_430582329311233] Generated 3 buff options for player 10102021301: [107, 109, 101]
[BuffManager_430582329311233] Generated 3 buff options for player 90000095932: [107, 103, 104]
[AutoChessScene_430582329311233] Generated 3 buff options for player 90000095932: [107, 103, 104]
[AutoChessScene_430582329311233] Player status: Total=4, Active=2
[AutoChessScene_430582329311233] Player 10102021301: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_430582329311233] Player 90000095932: Eliminated=False, Health=1, HasInstance=True
[AutoChessScene_430582329311233] Player 90000011436: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_430582329311233] Player 90000022651: Eliminated=True, Health=0, HasInstance=False
[AutoChessScene_430582329311233] Sending RoundStart notifications to 2 active players...
[AutoChessScene_430582329311233] RoundStart: Player 10102021301 buff options: [107, 109, 101]
[AutoChessScene_430582329311233] RoundStart board data: player:10102021301 heroes:25
[AutoChessScene_430582329311233] RoundStart board data: player:90000095932 heroes:20
[AutoChessScene_430582329311233] Sending RoundStart to Player 10102021301 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_430582329311233] RoundStart: Player 90000095932 buff options: [107, 103, 104]
[AutoChessScene_430582329311233] RoundStart board data: player:10102021301 heroes:25
[AutoChessScene_430582329311233] RoundStart board data: player:90000095932 heroes:20
[AutoChessScene_430582329311233] Sending RoundStart to Player 90000095932 on GameServer 10102
[AutoChessScene_430582329311233] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_430582329311233] Successfully sent RoundStart notifications to all 2 players via NATS
[BattleService] Updated battle 430582329311233 state to StateRoundStart
[AutoChessScene_430582329311233] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R6)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleStateManager_430582329311233] ===== ROUND 6 INITIALIZATION COMPLETE =====
[BattleStateManager_430582329311233] Buff selection timer started: 25000ms
[BattleStateManager_430582329311233] State: StateRoundStart -> StatePreparation (R6, 65000ms)
[AutoChessScene_430582329311233] Preparation phase started
[BuffManager_430582329311233] Added buff 107 (Buff_107) to player 90000095932
[AutoChessScene_430582329311233] Auto-selected buff 107 for bot player 90000095932
[CheckerBoard_430582329311233] Placed entity 46 at position (6, 4)
[CheckerBoard_430582329311233] Created entity ID:46 ConfigID:103 at (6, 4) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 47 at position (7, 5)
[CheckerBoard_430582329311233] Created entity ID:47 ConfigID:103 at (7, 5) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 48 at position (7, 6)
[CheckerBoard_430582329311233] Created entity ID:48 ConfigID:101 at (7, 6) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 49 at position (6, 3)
[CheckerBoard_430582329311233] Created entity ID:49 ConfigID:103 at (6, 3) for player 90000095932
[CheckerBoard_430582329311233] Placed entity 50 at position (7, 2)
[CheckerBoard_430582329311233] Created entity ID:50 ConfigID:101 at (7, 2) for player 90000095932
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 90000095932: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 90000095932 in Enemy area
[AutoChessScene_430582329311233] Generated 5 heroes for player 90000095932: 5 placed on board, 0 in temporary slots
[AutoChessScene_430582329311233] Generated 5 new heroes for bot player 90000095932 after buff selection
[AutoChessScene_430582329311233] No buff options available for bot player 90000011436
[AutoChessScene_430582329311233] No buff options available for bot player 90000022651
[PlayerManager_430582329311233] Player 90000095932 ready status set to True
[AutoChessScene_430582329311233] Auto-ready 1 additional bots
[AutoChessScene_430582329311233] Free operation phase started
[BattleService] Updated battle 430582329311233 state to StatePreparation
[AutoChessScene_430582329311233] State change sent to GameServer: StateRoundStart -> StatePreparation (R6)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED SelectBuffer RPC =====
[BattleService] Player 10102021301 is selecting buff 101
[BuffManager_430582329311233] Added buff 101 (Buff_101) to player 10102021301
[CheckerBoard_430582329311233] Placed entity 51 at position (2, 6)
[CheckerBoard_430582329311233] Created entity ID:51 ConfigID:103 at (2, 6) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 52 at position (1, 2)
[CheckerBoard_430582329311233] Created entity ID:52 ConfigID:103 at (1, 2) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 53 at position (5, 3)
[CheckerBoard_430582329311233] Created entity ID:53 ConfigID:101 at (5, 3) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 54 at position (2, 5)
[CheckerBoard_430582329311233] Created entity ID:54 ConfigID:102 at (2, 5) for player 10102021301
[CheckerBoard_430582329311233] Placed entity 55 at position (3, 1)
[CheckerBoard_430582329311233] Created entity ID:55 ConfigID:103 at (3, 1) for player 10102021301
[CheckerBoard_430582329311233] Generated 5/5 heroes for player 10102021301: 5 placed, 0 in temporary slots
[CheckerBoard_430582329311233] Generated 5 heroes for player 10102021301 in My area
[AutoChessScene_430582329311233] Generated 5 heroes for player 10102021301: 5 placed on board, 0 in temporary slots
[AutoChessScene_430582329311233] Player 10102021301 selected buff 101, generated 5 new heroes
[BattleService] SelectBuffer response: Code=0, NewHeroes=5
[PlayerManager_430582329311233] Player 10102021301 ready status set to True
[PlayerManager_430582329311233] All players are ready!
[AutoChessScene_430582329311233] All players are ready, transitioning to next state
[BattleStateManager_430582329311233] State: StatePreparation -> StateBattleStarting (R6, 1000ms)
[AutoChessScene_430582329311233] Applying battle start buffs for all players
[BuffManager_430582329311233] Applying battle start buff 105 (Buff_105) for player 10102021301
[BuffManager_430582329311233] Applying battle start buff 108 (Buff_108) for player 10102021301
[BuffManager_430582329311233] Applying battle start buff 101 (Buff_101) for player 10102021301
[BuffManager_430582329311233] Applying battle start buff 106 (Buff_106) for player 90000095932
[BuffManager_430582329311233] Applying battle start buff 108 (Buff_108) for player 90000095932
[BuffManager_430582329311233] Applying battle start buff 107 (Buff_107) for player 90000095932
[AutoChessScene_430582329311233] Camp info for player 10102021301: 30 heroes added
[AutoChessScene_430582329311233] Camp info for player 90000095932: 25 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 10102021301, Team order: [10102021301, 90000095932], total GridIDs used: 55
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 10102021301 vs Opponent 90000095932 with 2 teams
[AutoChessScene_430582329311233] Camp info for player 10102021301: 30 heroes added
[AutoChessScene_430582329311233] Camp info for player 90000095932: 25 heroes added
[AutoChessScene_430582329311233] Created RoundBattleStart request for player 90000095932, Team order: [10102021301, 90000095932], total GridIDs used: 55
[AutoChessScene_430582329311233] Sent RoundBattleStart to Player 90000095932 vs Opponent 10102021301 with 2 teams
[AutoChessScene_430582329311233] Sent RoundBattleStart notifications with seed: 689056166
[BattleService] Updated battle 430582329311233 state to StateBattleStarting
[AutoChessScene_430582329311233] State change sent to GameServer: StatePreparation -> StateBattleStarting (R6)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[AutoChessScene_430582329311233] Player 10102021301 set ready status to True
[BattleStateManager_430582329311233] State: StateBattleStarting -> StateBattleInProgress (R6, 65000ms)
[AutoChessScene_430582329311233] Starting all battle instances
[BattleInstance] 430582329311233_1 battle started
[BattleInstanceManager] Started all 1 battle instances
[AutoChessScene_430582329311233] Bot 90000095932 vs real player 10102021301, waiting for real player result
[AutoChessScene_430582329311233] Bot vs real player battles will be handled by system timeout (62s)
[BattleService] Updated battle 430582329311233 state to StateBattleInProgress
[AutoChessScene_430582329311233] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R6)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] EndBattle uid: 10102021301, win: True
[AutoChessScene_430582329311233] Player 10102021301 sent EndBattleReq (win: True), instance: 430582329311233_1
[AutoChessScene_430582329311233] Auto EndBattle for bot 90000095932 vs real player 10102021301, bot result: False
[BattleInstance] 430582329311233_1 battle finished, winner: 10102021301, loser: 90000095932
[AutoChessScene_430582329311233] Battle instance 430582329311233_1 completed: Winner 10102021301, Loser 90000095932
[AutoChessScene_430582329311233] All battle instances finished, proceeding to settlement
[BattleStateManager_430582329311233] State: StateBattleInProgress -> StateRoundSettlement (R6, 5000ms)
[AutoChessScene_430582329311233] Processing battle results
[PlayerManager_430582329311233] Player 90000095932 health reduced by 1, current health: 0
[PlayerManager_430582329311233] Player 90000095932 eliminated from battle 430582329311233
[AutoChessScene_430582329311233] Player 90000095932 has been eliminated
[AutoChessScene_430582329311233] Cleared 0 entities for player 90000095932
[PlayerManager_430582329311233] Game over! Winner: Player 10102021301
[AutoChessScene_430582329311233] Game over! Winner: Player 10102021301
[BattleStateManager_430582329311233] State: StateRoundSettlement -> StateGameOver (R6, 0ms)
[AutoChessScene_430582329311233] Game over processing
[AutoChessScene_430582329311233] Game winner: Player 10102021301
[AutoChessScene_430582329311233] Notifying GameServer: Game over, winner: 10102021301
[AutoChessScene_430582329311233] Player 10102021301 final lineup: 55 heroes
[AutoChessScene_430582329311233] Player 90000095932 final lineup: 55 heroes
[AutoChessScene_430582329311233] Player 90000011436 final lineup: 0 heroes
[AutoChessScene_430582329311233] Player 90000022651 final lineup: 0 heroes
[AutoChessScene_430582329311233] Sent BattleEnd notifications for 4 players
[AutoChessScene_430582329311233] Game over processing completed, waiting for cleanup signal
[BattleService] Updated battle 430582329311233 state to StateGameOver
[AutoChessScene_430582329311233] State change sent to GameServer: StateRoundSettlement -> StateGameOver (R6)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[AutoChessScene_430582329311233] Scheduling delayed cleanup in 5 seconds
[AutoChessScene_430582329311233] Player 90000095932 lost 1 health, winner: 10102021301
[AutoChessScene_430582329311233] Sent FINAL RoundBattleEnd notification (isEnd=true) - Winner: 10102021301, Loser: 90000095932
[BattleStateManager_430582329311233] Invalid state transition from StateGameOver to StateRoundSettlement
[AutoChessScene_430582329311233] Checking players elimination
[AutoChessScene_430582329311233] Active players remaining: 1
[AutoChessScene_430582329311233] Game ending, transitioning to StateGameOver
[BattleStateManager_430582329311233] Invalid state transition from StateGameOver to StateGameOver
[BattleService] Updated battle 430582329311233 state to StateRoundSettlement
[AutoChessScene_430582329311233] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R6)
[BattleStateManager_430582329311233] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_430582329311233] Executing delayed cleanup
[BattleService] Starting cleanup for battle 430582329311233
[BattleService] Removed battle state for 430582329311233
[CheckerBoard_430582329311233] Cleared all entities
[BuffManager_430582329311233] Cleared all buffs
[AutoChessScene_430582329311233] Scene resources disposed
[SceneManager] Removed AutoChessScene 430582329311233 from thread management
[BattleService] Cleaned up scene for battle 430582329311233
[BattleService] Cleanup completed for battle 430582329311233
Process finished with exit code -1,073,741,510.
